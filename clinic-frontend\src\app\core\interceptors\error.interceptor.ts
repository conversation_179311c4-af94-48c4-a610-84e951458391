import { HttpInterceptorFn, HttpErrorResponse } from '@angular/common/http';
import { inject } from '@angular/core';
import { Observable, throwError } from 'rxjs';
import { catchError, switchMap } from 'rxjs/operators';
import { AuthService } from '../services/auth.service';
import { Router } from '@angular/router';

export const errorInterceptor: HttpInterceptorFn = (req, next) => {
  const authService = inject(AuthService);
  const router = inject(Router);
  let isRefreshing = false;

  const handle401Error = (request: any, next: any): Observable<any> => {
    if (!isRefreshing) {
      isRefreshing = true;

      if (authService.isAuthenticated()) {
        return authService.refreshToken().pipe(
          switchMap(token => {
            isRefreshing = false;
            
            const authReq = request.clone({
              setHeaders: {
                Authorization: `Bearer ${token}`
              }
            });
            
            return next(authReq);
          }),
          catchError(error => {
            isRefreshing = false;
            authService.logout();
            router.navigate(['/login']);
            return throwError(() => error);
          })
        );
      }
    }
    
    isRefreshing = false;
    authService.logout();
    router.navigate(['/login']);
    return throwError(() => new Error('Session expired. Please login again.'));
  };

  return next(req).pipe(
    catchError((error: HttpErrorResponse) => {
      if (error.status === 401) {
        return handle401Error(req, next);
      } else if (error.status === 403) {
        router.navigate(['/unauthorized']);
      }
      
      const errorMessage = error.error?.message || error.statusText;
      return throwError(() => new Error(errorMessage));
    })
  );
}; 