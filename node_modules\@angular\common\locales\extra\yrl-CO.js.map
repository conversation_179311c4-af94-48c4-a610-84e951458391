{"version": 3, "file": "yrl-CO.js", "sourceRoot": "", "sources": ["yrl-CO.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,eAAe,CAAC,CAAC,CAAC,iBAAiB,EAAC,oBAAoB,EAAC,YAAY,EAAC,aAAa,EAAC,aAAa,EAAC,gBAAgB,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,OAAO,EAAC,OAAO,EAAC,CAAC,OAAO,EAAC,OAAO,CAAC,EAAC,CAAC,OAAO,EAAC,OAAO,CAAC,EAAC,CAAC,OAAO,EAAC,OAAO,CAAC,EAAC,CAAC,OAAO,EAAC,OAAO,CAAC,CAAC,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\n\nexport default [[[\"pituna pyterupé\",\"iandé-ara-pyturepé\",\"kuêma ramẽ\",\"karuka ramẽ\",\"pituna ramẽ\",\"pitunaeté ramẽ\"],u,u],u,[\"00:00\",\"12:00\",[\"06:00\",\"12:00\"],[\"12:00\",\"19:00\"],[\"19:00\",\"24:00\"],[\"00:00\",\"06:00\"]]];\n"]}