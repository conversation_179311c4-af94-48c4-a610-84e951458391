using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using ClinicManagement.Application.Features.Patients.Queries.GetPatientsList;
using ClinicManagement.Application.Features.Patients.Queries.GetPatientDetail;
using ClinicManagement.Application.Features.Patients.Commands.CreatePatient;
using ClinicManagement.Application.Features.Patients.Commands.UpdatePatient;
using ClinicManagement.Application.Features.Patients.Commands.DeletePatient;
using ClinicManagement.Application.Features.Patients.Queries.GetPatientById;
using ClinicManagement.Application.Features.Patients.Common;

namespace ClinicManagement.WebAPI.Controllers
{
    /// <summary>
    /// Frontend-compatible API endpoints for managing patients (returns unwrapped responses)
    /// </summary>
    [Route("api/frontend/[controller]")]
    [ApiController]
    [Produces("application/json")]
    public class FrontendPatientsController : ApiControllerBase
    {
        /// <summary>
        /// Gets a list of patients with optional filtering and pagination (unwrapped response)
        /// </summary>
        /// <param name="query">Query parameters for filtering and pagination</param>
        /// <returns>A list of patients matching the criteria</returns>
        /// <response code="200">Returns the list of patients</response>
        /// <response code="400">If the query parameters are invalid</response>
        [HttpGet]
        [Authorize]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<List<PatientDto>>> GetPatients([FromQuery] GetPatientsListQuery query)
        {
            var result = await Mediator.Send(query);
            return HandleResultUnwrapped(result);
        }

        /// <summary>
        /// Gets detailed information about a specific patient (unwrapped response)
        /// </summary>
        /// <param name="id">The ID of the patient</param>
        /// <returns>Detailed information about the patient</returns>
        /// <response code="200">Returns the patient details</response>
        /// <response code="404">If the patient is not found</response>
        [HttpGet("{id}/details")]
        [Authorize]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<PatientDetailDto>> GetPatientDetails(int id)
        {
            var query = new GetPatientDetailQuery { Id = id };
            var result = await Mediator.Send(query);
            return HandleResultUnwrapped(result);
        }

        /// <summary>
        /// Gets a patient by ID (unwrapped response)
        /// </summary>
        /// <param name="id">Patient ID</param>
        /// <returns>Patient details</returns>
        [HttpGet("{id}")]
        [Authorize]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<PatientDto>> GetById(int id)
        {
            var query = new GetPatientByIdQuery { Id = id };
            var result = await Mediator.Send(query);
            return HandleResultUnwrapped(result);
        }

        /// <summary>
        /// Creates a new patient (unwrapped response)
        /// </summary>
        /// <param name="command">Patient data</param>
        /// <returns>ID of the created patient</returns>
        [HttpPost]
        [Authorize]
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<int>> Create(CreatePatientCommand command)
        {
            var result = await Mediator.Send(command);
            return CreatedResultUnwrapped(result, nameof(GetById), new { id = result.Value });
        }

        /// <summary>
        /// Updates an existing patient (unwrapped response)
        /// </summary>
        /// <param name="id">Patient ID</param>
        /// <param name="command">Updated patient data</param>
        /// <returns>Success status</returns>
        [HttpPut("{id}")]
        [Authorize]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<bool>> Update(int id, UpdatePatientCommand command)
        {
            if (id != command.Id)
            {
                return BadRequest("Patient ID in URL does not match the ID in the request body");
            }

            var result = await Mediator.Send(command);
            return HandleResultUnwrapped(result);
        }

        /// <summary>
        /// Deletes a patient (unwrapped response)
        /// </summary>
        /// <param name="id">Patient ID</param>
        /// <returns>Success status</returns>
        [HttpDelete("{id}")]
        [Authorize]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<bool>> Delete(int id)
        {
            var command = new DeletePatientCommand { Id = id };
            var result = await Mediator.Send(command);
            return HandleResultUnwrapped(result);
        }
    }
}
