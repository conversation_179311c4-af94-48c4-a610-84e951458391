import { Routes } from '@angular/router';
import { authGuard } from './core/guards/auth.guard';
import { roleGuard } from './core/guards/role.guard';
import { UserRole } from './core/models/auth.model';

export const routes: Routes = [
  {
    path: '',
    redirectTo: 'dashboard',
    pathMatch: 'full'
  },
  {
    path: 'login',
    loadComponent: () => import('./features/auth/login/login.component').then(m => m.LoginComponent)
  },
  {
    path: 'register',
    loadComponent: () => import('./features/auth/register/register.component').then(m => m.RegisterComponent)
  },
  {
    path: 'initialize-admin',
    loadComponent: () => import('./features/auth/initialize-admin/initialize-admin.component').then(m => m.InitializeAdminComponent)
  },
  {
    path: '',
    loadComponent: () => import('./layouts/main-layout/main-layout.component').then(m => m.MainLayoutComponent),
    canActivate: [authGuard],
    children: [
      {
        path: 'dashboard',
        loadComponent: () => import('./features/dashboard/dashboard.component').then(m => m.DashboardComponent)
      },
      {
        path: 'patients',
        loadChildren: () => import('./features/patients/patient.routes').then(m => m.PATIENT_ROUTES),
        canActivate: [roleGuard],
        data: { roles: [UserRole.Admin, UserRole.Doctor, UserRole.Receptionist] }
      },
      {
        path: 'doctors',
        loadChildren: () => import('./features/doctors/doctors.routes').then(m => m.DOCTORS_ROUTES),
        canActivate: [roleGuard],
        data: { roles: [UserRole.Admin, UserRole.Receptionist] }
      },
      {
        path: 'appointments',
        loadChildren: () => import('./features/appointments/appointments.routes').then(m => m.APPOINTMENTS_ROUTES),
        canActivate: [roleGuard],
        data: { roles: [UserRole.Admin, UserRole.Doctor, UserRole.Receptionist, UserRole.Patient] }
      },
      {
        path: 'medical-records',
        loadChildren: () => import('./features/medical-records/medical-records.routes').then(m => m.MEDICAL_RECORDS_ROUTES),
        canActivate: [roleGuard],
        data: { roles: [UserRole.Admin, UserRole.Doctor] }
      },
      {
        path: 'schedules',
        loadChildren: () => import('./features/schedules/schedules.routes').then(m => m.SCHEDULES_ROUTES),
        canActivate: [roleGuard],
        data: { roles: [UserRole.Admin, UserRole.Doctor, UserRole.Receptionist] }
      }
    ]
  },
  {
    path: 'unauthorized',
    loadComponent: () => import('./features/auth/unauthorized/unauthorized.component').then(m => m.UnauthorizedComponent)
  },
  {
    path: '**',
    loadComponent: () => import('./features/auth/not-found/not-found.component').then(m => m.NotFoundComponent)
  }
];
