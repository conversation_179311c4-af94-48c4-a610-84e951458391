<div class="container mx-auto px-4 py-8">
  <div class="flex justify-between items-center mb-6">
    <h1 class="text-2xl font-bold">Appointments</h1>
    <button
      routerLink="new"
      class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
    >
      New Appointment
    </button>
  </div>

  <div class="mb-4">
    <input
      type="text"
      placeholder="Search appointments..."
      class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
      (input)="onSearch($any($event.target).value)"
    >
  </div>

  <div *ngIf="loading" class="flex justify-center items-center h-64">
    <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
  </div>

  <div *ngIf="!loading && appointments.length === 0" class="text-center py-8">
    <p class="text-gray-500">No appointments found.</p>
  </div>

  <div *ngIf="!loading && appointments.length > 0" class="bg-white shadow overflow-hidden sm:rounded-md">
    <ul class="divide-y divide-gray-200">
      <li *ngFor="let appointment of appointments">
        <div class="px-4 py-4 sm:px-6">
          <div class="flex items-center justify-between">
            <div class="flex-1 min-w-0">
              <p class="text-sm font-medium text-indigo-600 truncate">
                {{ appointment.patient?.firstName }} {{ appointment.patient?.lastName }} with Dr. {{ appointment.doctor?.firstName }} {{ appointment.doctor?.lastName }}
              </p>
              <p class="mt-1 text-sm text-gray-500">
                {{ appointment.date | date:'mediumDate' }} at {{ appointment.startTime }} - {{ appointment.endTime }}
              </p>
            </div>
            <div class="flex space-x-2">
              <button
                [routerLink]="[appointment.id]"
                class="px-3 py-1 text-sm text-indigo-600 hover:text-indigo-900"
              >
                View
              </button>
              <button
                [routerLink]="[appointment.id, 'edit']"
                class="px-3 py-1 text-sm text-gray-600 hover:text-gray-900"
              >
                Edit
              </button>
              <button
                (click)="onDelete(appointment.id)"
                class="px-3 py-1 text-sm text-red-600 hover:text-red-900"
              >
                Delete
              </button>
            </div>
          </div>
          <div class="mt-2">
            <span
              [ngClass]="{
                'bg-green-100 text-green-800': appointment.status === 'Completed',
                'bg-yellow-100 text-yellow-800': appointment.status === 'Scheduled',
                'bg-red-100 text-red-800': appointment.status === 'Cancelled' || appointment.status === 'NoShow'
              }"
              class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full"
            >
              {{ appointment.status }}
            </span>
          </div>
        </div>
      </li>
    </ul>
  </div>

  <div *ngIf="!loading && totalItems > 0" class="mt-4 flex justify-center">
    <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
      <button
        *ngFor="let page of [].constructor(Math.ceil(totalItems / pageSize)); let i = index"
        (click)="onPageChange(i + 1)"
        [class.bg-indigo-50]="currentPage === i + 1"
        class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50"
      >
        {{ i + 1 }}
      </button>
    </nav>
  </div>
</div> 