import { HttpClient, HttpErrorResponse, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { environment } from '../../../environments/environment';

@Injectable()
export abstract class BaseHttpService<T> {
  protected apiUrl: string;
  
  constructor(
    protected http: HttpClient,
    protected endpoint: string
  ) {
    this.apiUrl = `${environment.apiUrl}/${endpoint}`;
  }
  
  getAll(params?: any): Observable<T[]> {
    let httpParams = new HttpParams();
    
    if (params) {
      Object.keys(params).forEach(key => {
        if (params[key] !== null && params[key] !== undefined) {
          httpParams = httpParams.set(key, params[key]);
        }
      });
    }
    
    return this.http.get<T[]>(this.apiUrl, { params: httpParams });
  }
  
  getById(id: string): Observable<T> {
    return this.http.get<T>(`${this.apiUrl}/${id}`);
  }
  
  create(entity: Partial<T>): Observable<T> {
    return this.http.post<T>(this.apiUrl, entity);
  }
  
  update(id: string, entity: Partial<T>): Observable<T> {
    return this.http.put<T>(`${this.apiUrl}/${id}`, entity);
  }
  
  delete(id: string): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/${id}`);
  }
  
  protected handleError(error: HttpErrorResponse): Observable<never> {
    let errorMessage = 'An unknown error occurred';
    
    if (error.error instanceof ErrorEvent) {
      // Client-side error
      errorMessage = `Error: ${error.error.message}`;
    } else {
      // Server-side error
      errorMessage = error.error?.message || `Error Code: ${error.status}, Message: ${error.message}`;
    }
    
    return throwError(() => new Error(errorMessage));
  }

  protected getPaginatedEndpoint(endpoint: string): string {
    return `${this.apiUrl}/${endpoint}`;
  }
} 