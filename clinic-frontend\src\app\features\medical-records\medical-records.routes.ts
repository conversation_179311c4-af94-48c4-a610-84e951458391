import { Routes } from '@angular/router';

export const MEDICAL_RECORDS_ROUTES: Routes = [
  // Comment out missing component routes
  // {
  //   path: 'list',
  //   loadComponent: () => import('./medical-record-list/medical-record-list.component').then(m => m.MedicalRecordListComponent)
  // },
  // {
  //   path: 'new',
  //   loadComponent: () => import('./medical-record-form/medical-record-form.component').then(m => m.MedicalRecordFormComponent)
  // },
  // {
  //   path: 'edit/:id',
  //   loadComponent: () => import('./medical-record-form/medical-record-form.component').then(m => m.MedicalRecordFormComponent)
  // },
  // {
  //   path: ':id',
  //   loadComponent: () => import('./medical-record-detail/medical-record-detail.component').then(m => m.MedicalRecordDetailComponent)
  // },
  {
    path: '',
    loadComponent: () => import('./components/medical-record-list/medical-record-list.component').then(m => m.MedicalRecordListComponent)
  },
  {
    path: 'add',
    loadComponent: () => import('./components/medical-record-form/medical-record-form.component').then(m => m.MedicalRecordFormComponent)
  },
  {
    path: 'edit/:id',
    loadComponent: () => import('./components/medical-record-form/medical-record-form.component').then(m => m.MedicalRecordFormComponent)
  },
  {
    path: ':id',
    loadComponent: () => import('./components/medical-record-detail/medical-record-detail.component').then(m => m.MedicalRecordDetailComponent)
  }
]; 