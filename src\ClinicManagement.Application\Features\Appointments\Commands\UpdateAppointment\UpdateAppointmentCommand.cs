using System;
using ClinicManagement.Application.Common.Interfaces;
using ClinicManagement.Domain.Enums;

namespace ClinicManagement.Application.Features.Appointments.Commands.UpdateAppointment
{
    public class UpdateAppointmentCommand : ICommand<bool>
    {
        public int Id { get; set; }
        public int PatientId { get; set; }
        public int DoctorId { get; set; }
        public DateTime AppointmentDate { get; set; }
        public TimeSpan StartTime { get; set; }
        public TimeSpan EndTime { get; set; }
        public AppointmentType Type { get; set; }
        public AppointmentStatus Status { get; set; }
        public string? Notes { get; set; }
    }
}
