export enum Gender {
  Male = 1,
  Female = 2,
  Other = 3,
  PreferNotToSay = 4
}

export interface PersonName {
  firstName: string;
  lastName: string;
  middleName?: string;
  fullName: string;
  displayName: string;
}

export interface Email {
  value: string;
}

export interface PhoneNumber {
  value: string;
  formattedValue: string;
}

export interface Address {
  street: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
  fullAddress: string;
}

export interface Patient {
  id: number;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  address: string;
  city: string;
  state: string;
  postalCode: string;
  dateOfBirth: string;
  gender: Gender;
  createdAt: string;
  updatedAt: string;
  emergencyContactName?: string;
  emergencyContactPhone?: string;
  insuranceProvider?: string;
  insurancePolicyNumber?: string;
  medicalHistory?: string;
  allergies?: string;
}

export interface CreatePatientRequest {
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber: string;
  street: string;
  city: string;
  state: string;
  postalCode: string;
  dateOfBirth: string;
  gender: Gender;
}

export interface UpdatePatientRequest {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  address: string;
  city: string;
  state: string;
  postalCode: string;
  dateOfBirth: string;
  gender: Gender;
}

export interface PatientListResponse extends PaginatedResponse<Patient> {
  patients: Patient[];
}

export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  pageSize: number;
  currentPage: number;
}