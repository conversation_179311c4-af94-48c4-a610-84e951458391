import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, catchError, throwError } from 'rxjs';
import { Patient, CreatePatientRequest, UpdatePatientRequest } from '../models/patient.model';
import { environment } from '../../../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class PatientService {
  private apiUrl = `${environment.apiUrl}/frontend/patients`;  // Use frontend-compatible endpoints

  constructor(private http: HttpClient) {}

  getAll(pageNumber: number = 1, pageSize: number = 10, searchTerm?: string): Observable<Patient[]> {
    let params = new HttpParams()
      .set('pageNumber', pageNumber.toString())
      .set('pageSize', pageSize.toString())
      .set('skip', ((pageNumber - 1) * pageSize).toString())
      .set('take', pageSize.toString());

    if (searchTerm) {
      params = params.set('searchTerm', searchTerm);
    }

    return this.http.get<Patient[]>(this.apiUrl, { params }).pipe(
      catchError((error) => {
        console.error('Failed to fetch patients:', error);
        return throwError(() => error);
      })
    );
  }

  getById(id: number): Observable<Patient> {
    return this.http.get<Patient>(`${this.apiUrl}/${id}`).pipe(
      catchError((error) => {
        console.error(`Failed to fetch patient ${id}:`, error);
        return throwError(() => error);
      })
    );
  }

  getDetails(id: number): Observable<Patient> {
    return this.http.get<Patient>(`${this.apiUrl}/${id}/details`).pipe(
      catchError((error) => {
        console.error(`Failed to fetch patient details ${id}:`, error);
        return throwError(() => error);
      })
    );
  }

  create(patient: CreatePatientRequest): Observable<number> {
    return this.http.post<number>(this.apiUrl, patient).pipe(
      catchError((error) => {
        console.error('Failed to create patient:', error);
        return throwError(() => error);
      })
    );
  }

  update(id: number, patient: UpdatePatientRequest): Observable<boolean> {
    patient.id = id; // Ensure ID is set
    return this.http.put<boolean>(`${this.apiUrl}/${id}`, patient).pipe(
      catchError((error) => {
        console.error(`Failed to update patient ${id}:`, error);
        return throwError(() => error);
      })
    );
  }

  delete(id: number): Observable<boolean> {
    return this.http.delete<boolean>(`${this.apiUrl}/${id}`).pipe(
      catchError((error) => {
        console.error(`Failed to delete patient ${id}:`, error);
        return throwError(() => error);
      })
    );
  }
}