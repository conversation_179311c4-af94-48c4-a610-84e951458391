﻿++Solution 'ClinicManagement' ‎ (4 of 4 projects)
i:{00000000-0000-0000-0000-000000000000}:ClinicManagement.sln
++src
i:{00000000-0000-0000-0000-000000000000}:src
++ClinicManagement.Domain
i:{827e0cd3-b72d-47b6-a68d-7590b98eb39b}:ClinicManagement.Domain
i:{a39610dd-838f-46d5-8f11-c5e7b562a762}:>819
++Dependencies
i:{94826b6e-2786-4c44-a29f-deaff0642f25}:>812
i:{6594684e-1af1-4043-9dd5-d0c8ccceb2de}:>813
i:{d0bb96d1-75ee-4922-b836-25f09b81cb55}:>817
i:{a39610dd-838f-46d5-8f11-c5e7b562a762}:>814
++Entities
i:{94826b6e-2786-4c44-a29f-deaff0642f25}:f:\clinc\src\clinicmanagement.domain\entities\
++Appointment.cs
i:{94826b6e-2786-4c44-a29f-deaff0642f25}:f:\clinc\src\clinicmanagement.domain\entities\appointment.cs
++BaseEntity.cs
i:{94826b6e-2786-4c44-a29f-deaff0642f25}:f:\clinc\src\clinicmanagement.domain\entities\baseentity.cs
++Doctor.cs
i:{94826b6e-2786-4c44-a29f-deaff0642f25}:f:\clinc\src\clinicmanagement.domain\entities\doctor.cs
++MedicalRecord.cs
i:{94826b6e-2786-4c44-a29f-deaff0642f25}:f:\clinc\src\clinicmanagement.domain\entities\medicalrecord.cs
++Patient.cs
i:{94826b6e-2786-4c44-a29f-deaff0642f25}:f:\clinc\src\clinicmanagement.domain\entities\patient.cs
++Schedule.cs
i:{94826b6e-2786-4c44-a29f-deaff0642f25}:f:\clinc\src\clinicmanagement.domain\entities\schedule.cs
++Enums
i:{94826b6e-2786-4c44-a29f-deaff0642f25}:f:\clinc\src\clinicmanagement.domain\enums\
++AppointmentStatus.cs
i:{94826b6e-2786-4c44-a29f-deaff0642f25}:f:\clinc\src\clinicmanagement.domain\enums\appointmentstatus.cs
++AppointmentType.cs
i:{94826b6e-2786-4c44-a29f-deaff0642f25}:f:\clinc\src\clinicmanagement.domain\enums\appointmenttype.cs
++Gender.cs
i:{94826b6e-2786-4c44-a29f-deaff0642f25}:f:\clinc\src\clinicmanagement.domain\enums\gender.cs
++Events
i:{94826b6e-2786-4c44-a29f-deaff0642f25}:f:\clinc\src\clinicmanagement.domain\events\
++AppointmentEvents.cs
i:{94826b6e-2786-4c44-a29f-deaff0642f25}:f:\clinc\src\clinicmanagement.domain\events\appointmentevents.cs
++IDomainEvent.cs
i:{94826b6e-2786-4c44-a29f-deaff0642f25}:f:\clinc\src\clinicmanagement.domain\events\idomainevent.cs
++PatientEvents.cs
i:{94826b6e-2786-4c44-a29f-deaff0642f25}:f:\clinc\src\clinicmanagement.domain\events\patientevents.cs
++Exceptions
i:{94826b6e-2786-4c44-a29f-deaff0642f25}:f:\clinc\src\clinicmanagement.domain\exceptions\
++DomainException.cs
i:{94826b6e-2786-4c44-a29f-deaff0642f25}:f:\clinc\src\clinicmanagement.domain\exceptions\domainexception.cs
++Repositories
i:{94826b6e-2786-4c44-a29f-deaff0642f25}:f:\clinc\src\clinicmanagement.domain\repositories\
i:{6594684e-1af1-4043-9dd5-d0c8ccceb2de}:f:\clinc\src\clinicmanagement.infrastructure\repositories\
++IRepository.cs
i:{94826b6e-2786-4c44-a29f-deaff0642f25}:f:\clinc\src\clinicmanagement.domain\repositories\irepository.cs
++IUnitOfWork.cs
i:{94826b6e-2786-4c44-a29f-deaff0642f25}:f:\clinc\src\clinicmanagement.domain\repositories\iunitofwork.cs
++Specifications
i:{94826b6e-2786-4c44-a29f-deaff0642f25}:f:\clinc\src\clinicmanagement.domain\specifications\
++AppointmentSpecifications.cs
i:{94826b6e-2786-4c44-a29f-deaff0642f25}:f:\clinc\src\clinicmanagement.domain\specifications\appointmentspecifications.cs
++BaseSpecification.cs
i:{94826b6e-2786-4c44-a29f-deaff0642f25}:f:\clinc\src\clinicmanagement.domain\specifications\basespecification.cs
++ISpecification.cs
i:{94826b6e-2786-4c44-a29f-deaff0642f25}:f:\clinc\src\clinicmanagement.domain\specifications\ispecification.cs
++MedicalRecordSpecifications.cs
i:{94826b6e-2786-4c44-a29f-deaff0642f25}:f:\clinc\src\clinicmanagement.domain\specifications\medicalrecordspecifications.cs
++PatientSpecifications.cs
i:{94826b6e-2786-4c44-a29f-deaff0642f25}:f:\clinc\src\clinicmanagement.domain\specifications\patientspecifications.cs
++ScheduleSpecifications.cs
i:{94826b6e-2786-4c44-a29f-deaff0642f25}:f:\clinc\src\clinicmanagement.domain\specifications\schedulespecifications.cs
++ValueObjects
i:{94826b6e-2786-4c44-a29f-deaff0642f25}:f:\clinc\src\clinicmanagement.domain\valueobjects\
++Address.cs
i:{94826b6e-2786-4c44-a29f-deaff0642f25}:f:\clinc\src\clinicmanagement.domain\valueobjects\address.cs
++Email.cs
i:{94826b6e-2786-4c44-a29f-deaff0642f25}:f:\clinc\src\clinicmanagement.domain\valueobjects\email.cs
++PersonName.cs
i:{94826b6e-2786-4c44-a29f-deaff0642f25}:f:\clinc\src\clinicmanagement.domain\valueobjects\personname.cs
++PhoneNumber.cs
i:{94826b6e-2786-4c44-a29f-deaff0642f25}:f:\clinc\src\clinicmanagement.domain\valueobjects\phonenumber.cs
++ValueObject.cs
i:{94826b6e-2786-4c44-a29f-deaff0642f25}:f:\clinc\src\clinicmanagement.domain\valueobjects\valueobject.cs
++Analyzers
i:{94826b6e-2786-4c44-a29f-deaff0642f25}:>840
i:{6594684e-1af1-4043-9dd5-d0c8ccceb2de}:>886
i:{d0bb96d1-75ee-4922-b836-25f09b81cb55}:>856
i:{a39610dd-838f-46d5-8f11-c5e7b562a762}:>820
++Frameworks
i:{94826b6e-2786-4c44-a29f-deaff0642f25}:>849
i:{6594684e-1af1-4043-9dd5-d0c8ccceb2de}:>898
i:{d0bb96d1-75ee-4922-b836-25f09b81cb55}:>877
i:{a39610dd-838f-46d5-8f11-c5e7b562a762}:>831
++Packages
i:{94826b6e-2786-4c44-a29f-deaff0642f25}:>851
i:{6594684e-1af1-4043-9dd5-d0c8ccceb2de}:>900
i:{d0bb96d1-75ee-4922-b836-25f09b81cb55}:>880
i:{a39610dd-838f-46d5-8f11-c5e7b562a762}:>833
++Microsoft.CodeAnalysis.CSharp.NetAnalyzers
i:{94826b6e-2786-4c44-a29f-deaff0642f25}:c:\program files\dotnet\sdk\9.0.201\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.csharp.netanalyzers.dll
i:{6594684e-1af1-4043-9dd5-d0c8ccceb2de}:c:\program files\dotnet\sdk\9.0.201\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.csharp.netanalyzers.dll
i:{d0bb96d1-75ee-4922-b836-25f09b81cb55}:c:\program files\dotnet\sdk\9.0.201\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.csharp.netanalyzers.dll
i:{a39610dd-838f-46d5-8f11-c5e7b562a762}:c:\program files\dotnet\sdk\9.0.201\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.csharp.netanalyzers.dll
++Microsoft.CodeAnalysis.NetAnalyzers
i:{94826b6e-2786-4c44-a29f-deaff0642f25}:c:\program files\dotnet\sdk\9.0.201\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.netanalyzers.dll
i:{6594684e-1af1-4043-9dd5-d0c8ccceb2de}:c:\program files\dotnet\sdk\9.0.201\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.netanalyzers.dll
i:{d0bb96d1-75ee-4922-b836-25f09b81cb55}:c:\program files\dotnet\sdk\9.0.201\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.netanalyzers.dll
i:{a39610dd-838f-46d5-8f11-c5e7b562a762}:c:\program files\dotnet\sdk\9.0.201\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.netanalyzers.dll
++Microsoft.Interop.ComInterfaceGenerator
i:{94826b6e-2786-4c44-a29f-deaff0642f25}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.3\analyzers\dotnet\cs\microsoft.interop.cominterfacegenerator.dll
i:{6594684e-1af1-4043-9dd5-d0c8ccceb2de}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.3\analyzers\dotnet\cs\microsoft.interop.cominterfacegenerator.dll
i:{d0bb96d1-75ee-4922-b836-25f09b81cb55}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.3\analyzers\dotnet\cs\microsoft.interop.cominterfacegenerator.dll
i:{a39610dd-838f-46d5-8f11-c5e7b562a762}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.3\analyzers\dotnet\cs\microsoft.interop.cominterfacegenerator.dll
++Microsoft.Interop.JavaScript.JSImportGenerator
i:{94826b6e-2786-4c44-a29f-deaff0642f25}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.3\analyzers\dotnet\cs\microsoft.interop.javascript.jsimportgenerator.dll
i:{6594684e-1af1-4043-9dd5-d0c8ccceb2de}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.3\analyzers\dotnet\cs\microsoft.interop.javascript.jsimportgenerator.dll
i:{d0bb96d1-75ee-4922-b836-25f09b81cb55}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.3\analyzers\dotnet\cs\microsoft.interop.javascript.jsimportgenerator.dll
i:{a39610dd-838f-46d5-8f11-c5e7b562a762}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.3\analyzers\dotnet\cs\microsoft.interop.javascript.jsimportgenerator.dll
++Microsoft.Interop.LibraryImportGenerator
i:{94826b6e-2786-4c44-a29f-deaff0642f25}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.3\analyzers\dotnet\cs\microsoft.interop.libraryimportgenerator.dll
i:{6594684e-1af1-4043-9dd5-d0c8ccceb2de}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.3\analyzers\dotnet\cs\microsoft.interop.libraryimportgenerator.dll
i:{d0bb96d1-75ee-4922-b836-25f09b81cb55}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.3\analyzers\dotnet\cs\microsoft.interop.libraryimportgenerator.dll
i:{a39610dd-838f-46d5-8f11-c5e7b562a762}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.3\analyzers\dotnet\cs\microsoft.interop.libraryimportgenerator.dll
++Microsoft.Interop.SourceGeneration
i:{94826b6e-2786-4c44-a29f-deaff0642f25}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.3\analyzers\dotnet\cs\microsoft.interop.sourcegeneration.dll
i:{6594684e-1af1-4043-9dd5-d0c8ccceb2de}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.3\analyzers\dotnet\cs\microsoft.interop.sourcegeneration.dll
i:{d0bb96d1-75ee-4922-b836-25f09b81cb55}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.3\analyzers\dotnet\cs\microsoft.interop.sourcegeneration.dll
i:{a39610dd-838f-46d5-8f11-c5e7b562a762}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.3\analyzers\dotnet\cs\microsoft.interop.sourcegeneration.dll
++System.Text.Json.SourceGeneration
i:{94826b6e-2786-4c44-a29f-deaff0642f25}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.3\analyzers\dotnet\cs\system.text.json.sourcegeneration.dll
i:{6594684e-1af1-4043-9dd5-d0c8ccceb2de}:c:\users\<USER>\.nuget\packages\system.text.json\9.0.5\analyzers\dotnet\roslyn4.4\cs\system.text.json.sourcegeneration.dll
i:{d0bb96d1-75ee-4922-b836-25f09b81cb55}:c:\users\<USER>\.nuget\packages\system.text.json\9.0.5\analyzers\dotnet\roslyn4.4\cs\system.text.json.sourcegeneration.dll
i:{a39610dd-838f-46d5-8f11-c5e7b562a762}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.3\analyzers\dotnet\cs\system.text.json.sourcegeneration.dll
++System.Text.RegularExpressions.Generator
i:{94826b6e-2786-4c44-a29f-deaff0642f25}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.3\analyzers\dotnet\cs\system.text.regularexpressions.generator.dll
i:{6594684e-1af1-4043-9dd5-d0c8ccceb2de}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.3\analyzers\dotnet\cs\system.text.regularexpressions.generator.dll
i:{d0bb96d1-75ee-4922-b836-25f09b81cb55}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.3\analyzers\dotnet\cs\system.text.regularexpressions.generator.dll
i:{a39610dd-838f-46d5-8f11-c5e7b562a762}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.3\analyzers\dotnet\cs\system.text.regularexpressions.generator.dll
++Microsoft.NETCore.App
i:{94826b6e-2786-4c44-a29f-deaff0642f25}:>850
i:{6594684e-1af1-4043-9dd5-d0c8ccceb2de}:>899
i:{d0bb96d1-75ee-4922-b836-25f09b81cb55}:>879
i:{a39610dd-838f-46d5-8f11-c5e7b562a762}:>832
++MediatR (12.5.0)
i:{94826b6e-2786-4c44-a29f-deaff0642f25}:>852
i:{a39610dd-838f-46d5-8f11-c5e7b562a762}:>836
++ClinicManagement.Infrastructure
i:{827e0cd3-b72d-47b6-a68d-7590b98eb39b}:ClinicManagement.Infrastructure
i:{d0bb96d1-75ee-4922-b836-25f09b81cb55}:>854
++Data
i:{6594684e-1af1-4043-9dd5-d0c8ccceb2de}:f:\clinc\src\clinicmanagement.infrastructure\data\
++ClinicDbContext.cs
i:{6594684e-1af1-4043-9dd5-d0c8ccceb2de}:f:\clinc\src\clinicmanagement.infrastructure\data\clinicdbcontext.cs
++SpecificationEvaluator.cs
i:{6594684e-1af1-4043-9dd5-d0c8ccceb2de}:f:\clinc\src\clinicmanagement.infrastructure\data\specificationevaluator.cs
++AppointmentRepository.cs
i:{6594684e-1af1-4043-9dd5-d0c8ccceb2de}:f:\clinc\src\clinicmanagement.infrastructure\repositories\appointmentrepository.cs
++DoctorRepository.cs
i:{6594684e-1af1-4043-9dd5-d0c8ccceb2de}:f:\clinc\src\clinicmanagement.infrastructure\repositories\doctorrepository.cs
++MedicalRecordRepository.cs
i:{6594684e-1af1-4043-9dd5-d0c8ccceb2de}:f:\clinc\src\clinicmanagement.infrastructure\repositories\medicalrecordrepository.cs
++PatientRepository.cs
i:{6594684e-1af1-4043-9dd5-d0c8ccceb2de}:f:\clinc\src\clinicmanagement.infrastructure\repositories\patientrepository.cs
++Repository.cs
i:{6594684e-1af1-4043-9dd5-d0c8ccceb2de}:f:\clinc\src\clinicmanagement.infrastructure\repositories\repository.cs
++ScheduleRepository.cs
i:{6594684e-1af1-4043-9dd5-d0c8ccceb2de}:f:\clinc\src\clinicmanagement.infrastructure\repositories\schedulerepository.cs
++UnitOfWork.cs
i:{6594684e-1af1-4043-9dd5-d0c8ccceb2de}:f:\clinc\src\clinicmanagement.infrastructure\repositories\unitofwork.cs
++Services
i:{6594684e-1af1-4043-9dd5-d0c8ccceb2de}:f:\clinc\src\clinicmanagement.infrastructure\services\
++CurrentUserService.cs
i:{6594684e-1af1-4043-9dd5-d0c8ccceb2de}:f:\clinc\src\clinicmanagement.infrastructure\services\currentuserservice.cs
++DomainEventService.cs
i:{6594684e-1af1-4043-9dd5-d0c8ccceb2de}:f:\clinc\src\clinicmanagement.infrastructure\services\domaineventservice.cs
++Class1.cs
i:{6594684e-1af1-4043-9dd5-d0c8ccceb2de}:f:\clinc\src\clinicmanagement.infrastructure\class1.cs
i:{a39610dd-838f-46d5-8f11-c5e7b562a762}:f:\clinc\src\clinicmanagement.application\class1.cs
++DependencyInjection.cs
i:{6594684e-1af1-4043-9dd5-d0c8ccceb2de}:f:\clinc\src\clinicmanagement.infrastructure\dependencyinjection.cs
i:{a39610dd-838f-46d5-8f11-c5e7b562a762}:f:\clinc\src\clinicmanagement.application\dependencyinjection.cs
++Projects
i:{6594684e-1af1-4043-9dd5-d0c8ccceb2de}:>884
i:{d0bb96d1-75ee-4922-b836-25f09b81cb55}:>853
i:{a39610dd-838f-46d5-8f11-c5e7b562a762}:>818
++Microsoft.EntityFrameworkCore.Analyzers
i:{6594684e-1af1-4043-9dd5-d0c8ccceb2de}:c:\users\<USER>\.nuget\packages\microsoft.entityframeworkcore.analyzers\9.0.5\analyzers\dotnet\cs\microsoft.entityframeworkcore.analyzers.dll
i:{d0bb96d1-75ee-4922-b836-25f09b81cb55}:c:\users\<USER>\.nuget\packages\microsoft.entityframeworkcore.analyzers\9.0.5\analyzers\dotnet\cs\microsoft.entityframeworkcore.analyzers.dll
++Microsoft.Extensions.Logging.Generators
i:{6594684e-1af1-4043-9dd5-d0c8ccceb2de}:c:\users\<USER>\.nuget\packages\microsoft.extensions.logging.abstractions\9.0.5\analyzers\dotnet\roslyn4.4\cs\microsoft.extensions.logging.generators.dll
i:{d0bb96d1-75ee-4922-b836-25f09b81cb55}:c:\users\<USER>\.nuget\packages\microsoft.extensions.logging.abstractions\9.0.5\analyzers\dotnet\roslyn4.4\cs\microsoft.extensions.logging.generators.dll
i:{a39610dd-838f-46d5-8f11-c5e7b562a762}:c:\users\<USER>\.nuget\packages\microsoft.extensions.logging.abstractions\9.0.0-preview.5.24306.7\analyzers\dotnet\roslyn4.4\cs\microsoft.extensions.logging.generators.dll
++Microsoft.Extensions.Options.SourceGeneration
i:{6594684e-1af1-4043-9dd5-d0c8ccceb2de}:c:\users\<USER>\.nuget\packages\microsoft.extensions.options\9.0.5\analyzers\dotnet\roslyn4.4\cs\microsoft.extensions.options.sourcegeneration.dll
i:{d0bb96d1-75ee-4922-b836-25f09b81cb55}:c:\users\<USER>\.nuget\packages\microsoft.extensions.options\9.0.5\analyzers\dotnet\roslyn4.4\cs\microsoft.extensions.options.sourcegeneration.dll
i:{a39610dd-838f-46d5-8f11-c5e7b562a762}:c:\users\<USER>\.nuget\packages\microsoft.extensions.options\8.0.0\analyzers\dotnet\roslyn4.4\cs\microsoft.extensions.options.sourcegeneration.dll
++Microsoft.AspNetCore.Http.Abstractions (2.3.0)
i:{6594684e-1af1-4043-9dd5-d0c8ccceb2de}:>904
++Microsoft.EntityFrameworkCore.SqlServer (9.0.5)
++Microsoft.Extensions.Configuration (9.0.5)
i:{6594684e-1af1-4043-9dd5-d0c8ccceb2de}:>902
++System.Security.Claims (4.3.0)
i:{6594684e-1af1-4043-9dd5-d0c8ccceb2de}:>903
++ClinicManagement.Application
i:{6594684e-1af1-4043-9dd5-d0c8ccceb2de}:>885
i:{d0bb96d1-75ee-4922-b836-25f09b81cb55}:>855
i:{827e0cd3-b72d-47b6-a68d-7590b98eb39b}:ClinicManagement.Application
++ClinicManagement.WebAPI
i:{827e0cd3-b72d-47b6-a68d-7590b98eb39b}:ClinicManagement.WebAPI
++Connected Services 
i:{d0bb96d1-75ee-4922-b836-25f09b81cb55}:>815
++Properties
i:{d0bb96d1-75ee-4922-b836-25f09b81cb55}:f:\clinc\src\clinicmanagement.webapi\properties\
++launchSettings - Copy.json
i:{d0bb96d1-75ee-4922-b836-25f09b81cb55}:f:\clinc\src\clinicmanagement.webapi\properties\launchsettings - copy.json
++launchSettings.json
i:{d0bb96d1-75ee-4922-b836-25f09b81cb55}:f:\clinc\src\clinicmanagement.webapi\properties\launchsettings.json
++Controllers
i:{d0bb96d1-75ee-4922-b836-25f09b81cb55}:f:\clinc\src\clinicmanagement.webapi\controllers\
++ApiControllerBase.cs
i:{d0bb96d1-75ee-4922-b836-25f09b81cb55}:f:\clinc\src\clinicmanagement.webapi\controllers\apicontrollerbase.cs
++AppointmentsController.cs
i:{d0bb96d1-75ee-4922-b836-25f09b81cb55}:f:\clinc\src\clinicmanagement.webapi\controllers\appointmentscontroller.cs
++DoctorsController.cs
i:{d0bb96d1-75ee-4922-b836-25f09b81cb55}:f:\clinc\src\clinicmanagement.webapi\controllers\doctorscontroller.cs
++MedicalRecordsController.cs
i:{d0bb96d1-75ee-4922-b836-25f09b81cb55}:f:\clinc\src\clinicmanagement.webapi\controllers\medicalrecordscontroller.cs
++PatientsController.cs
i:{d0bb96d1-75ee-4922-b836-25f09b81cb55}:f:\clinc\src\clinicmanagement.webapi\controllers\patientscontroller.cs
++SchedulesController.cs
i:{d0bb96d1-75ee-4922-b836-25f09b81cb55}:f:\clinc\src\clinicmanagement.webapi\controllers\schedulescontroller.cs
++Middleware
i:{d0bb96d1-75ee-4922-b836-25f09b81cb55}:f:\clinc\src\clinicmanagement.webapi\middleware\
++appsettings.json
i:{d0bb96d1-75ee-4922-b836-25f09b81cb55}:f:\clinc\src\clinicmanagement.webapi\appsettings.json
++ClinicManagement.WebAPI.http
i:{d0bb96d1-75ee-4922-b836-25f09b81cb55}:f:\clinc\src\clinicmanagement.webapi\clinicmanagement.webapi.http
++Program.cs
i:{d0bb96d1-75ee-4922-b836-25f09b81cb55}:f:\clinc\src\clinicmanagement.webapi\program.cs
++No service dependencies discovered
i:{d0bb96d1-75ee-4922-b836-25f09b81cb55}:>816
++ExceptionHandlingMiddleware.cs
i:{d0bb96d1-75ee-4922-b836-25f09b81cb55}:f:\clinc\src\clinicmanagement.webapi\middleware\exceptionhandlingmiddleware.cs
++MiddlewareExtensions.cs
i:{d0bb96d1-75ee-4922-b836-25f09b81cb55}:f:\clinc\src\clinicmanagement.webapi\middleware\middlewareextensions.cs
++appsettings.Development.json
i:{d0bb96d1-75ee-4922-b836-25f09b81cb55}:f:\clinc\src\clinicmanagement.webapi\appsettings.development.json
++Microsoft.AspNetCore.Analyzers
i:{d0bb96d1-75ee-4922-b836-25f09b81cb55}:c:\program files\dotnet\sdk\9.0.201\sdks\microsoft.net.sdk.web\analyzers\cs\microsoft.aspnetcore.analyzers.dll
++Microsoft.AspNetCore.App.Analyzers
i:{d0bb96d1-75ee-4922-b836-25f09b81cb55}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\9.0.3\analyzers\dotnet\cs\microsoft.aspnetcore.app.analyzers.dll
++Microsoft.AspNetCore.App.CodeFixes
i:{d0bb96d1-75ee-4922-b836-25f09b81cb55}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\9.0.3\analyzers\dotnet\cs\microsoft.aspnetcore.app.codefixes.dll
++Microsoft.AspNetCore.Components.Analyzers
i:{d0bb96d1-75ee-4922-b836-25f09b81cb55}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\9.0.3\analyzers\dotnet\cs\microsoft.aspnetcore.components.analyzers.dll
++Microsoft.AspNetCore.Mvc.Analyzers
i:{d0bb96d1-75ee-4922-b836-25f09b81cb55}:c:\program files\dotnet\sdk\9.0.201\sdks\microsoft.net.sdk.web\analyzers\cs\microsoft.aspnetcore.mvc.analyzers.dll
++Microsoft.AspNetCore.Razor.Utilities.Shared
i:{d0bb96d1-75ee-4922-b836-25f09b81cb55}:c:\program files\dotnet\sdk\9.0.201\sdks\microsoft.net.sdk.razor\source-generators\microsoft.aspnetcore.razor.utilities.shared.dll
++Microsoft.CodeAnalysis.Razor.Compiler
i:{d0bb96d1-75ee-4922-b836-25f09b81cb55}:c:\program files\dotnet\sdk\9.0.201\sdks\microsoft.net.sdk.razor\source-generators\microsoft.codeanalysis.razor.compiler.dll
++Microsoft.Extensions.ObjectPool
i:{d0bb96d1-75ee-4922-b836-25f09b81cb55}:c:\program files\dotnet\sdk\9.0.201\sdks\microsoft.net.sdk.razor\source-generators\microsoft.extensions.objectpool.dll
++System.Collections.Immutable
i:{d0bb96d1-75ee-4922-b836-25f09b81cb55}:c:\program files\dotnet\sdk\9.0.201\sdks\microsoft.net.sdk.razor\source-generators\system.collections.immutable.dll
++Microsoft.AspNetCore.App
i:{d0bb96d1-75ee-4922-b836-25f09b81cb55}:>878
++Microsoft.AspNetCore.Authentication.JwtBearer (9.0.3)
++Microsoft.AspNetCore.OpenApi (9.0.3)
++Scalar.AspNetCore (2.4.4)
i:{d0bb96d1-75ee-4922-b836-25f09b81cb55}:>907
++Swashbuckle.AspNetCore (8.1.1)
++AutoMapper (14.0.0)
i:{a39610dd-838f-46d5-8f11-c5e7b562a762}:>838
++FluentValidation (12.0.0)
i:{a39610dd-838f-46d5-8f11-c5e7b562a762}:>837
++FluentValidation.DependencyInjectionExtensions (12.0.0)
i:{a39610dd-838f-46d5-8f11-c5e7b562a762}:>835
++Microsoft.Extensions.Logging.Abstractions (9.0.5)
i:{a39610dd-838f-46d5-8f11-c5e7b562a762}:>834
++Serilog (4.3.0)
i:{a39610dd-838f-46d5-8f11-c5e7b562a762}:>839
++Common
i:{a39610dd-838f-46d5-8f11-c5e7b562a762}:f:\clinc\src\clinicmanagement.application\common\
i:{a39610dd-838f-46d5-8f11-c5e7b562a762}:f:\clinc\src\clinicmanagement.application\features\appointments\common\
i:{a39610dd-838f-46d5-8f11-c5e7b562a762}:f:\clinc\src\clinicmanagement.application\features\medicalrecords\common\
i:{a39610dd-838f-46d5-8f11-c5e7b562a762}:f:\clinc\src\clinicmanagement.application\features\patients\common\
i:{a39610dd-838f-46d5-8f11-c5e7b562a762}:f:\clinc\src\clinicmanagement.application\features\schedules\common\
++Behaviors
i:{a39610dd-838f-46d5-8f11-c5e7b562a762}:f:\clinc\src\clinicmanagement.application\common\behaviors\
++DomainEventBehavior.cs
i:{a39610dd-838f-46d5-8f11-c5e7b562a762}:f:\clinc\src\clinicmanagement.application\common\behaviors\domaineventbehavior.cs
++LoggingBehavior.cs
i:{a39610dd-838f-46d5-8f11-c5e7b562a762}:f:\clinc\src\clinicmanagement.application\common\behaviors\loggingbehavior.cs
++PerformanceBehavior.cs
i:{a39610dd-838f-46d5-8f11-c5e7b562a762}:f:\clinc\src\clinicmanagement.application\common\behaviors\performancebehavior.cs
++UnhandledExceptionBehavior.cs
i:{a39610dd-838f-46d5-8f11-c5e7b562a762}:f:\clinc\src\clinicmanagement.application\common\behaviors\unhandledexceptionbehavior.cs
++ValidationBehavior.cs
i:{a39610dd-838f-46d5-8f11-c5e7b562a762}:f:\clinc\src\clinicmanagement.application\common\behaviors\validationbehavior.cs
++Interfaces
i:{a39610dd-838f-46d5-8f11-c5e7b562a762}:f:\clinc\src\clinicmanagement.application\common\interfaces\
++Mappings
i:{a39610dd-838f-46d5-8f11-c5e7b562a762}:f:\clinc\src\clinicmanagement.application\common\mappings\
++IMapFrom.cs
i:{a39610dd-838f-46d5-8f11-c5e7b562a762}:f:\clinc\src\clinicmanagement.application\common\mappings\imapfrom.cs
++MappingProfile.cs
i:{a39610dd-838f-46d5-8f11-c5e7b562a762}:f:\clinc\src\clinicmanagement.application\common\mappings\mappingprofile.cs
++Models
i:{a39610dd-838f-46d5-8f11-c5e7b562a762}:f:\clinc\src\clinicmanagement.application\common\models\
++PaginatedList.cs
i:{a39610dd-838f-46d5-8f11-c5e7b562a762}:f:\clinc\src\clinicmanagement.application\common\models\paginatedlist.cs
++Result.cs
i:{a39610dd-838f-46d5-8f11-c5e7b562a762}:f:\clinc\src\clinicmanagement.application\common\models\result.cs
++Features
i:{a39610dd-838f-46d5-8f11-c5e7b562a762}:f:\clinc\src\clinicmanagement.application\features\
++Appointments
i:{a39610dd-838f-46d5-8f11-c5e7b562a762}:f:\clinc\src\clinicmanagement.application\features\appointments\
++Commands
i:{a39610dd-838f-46d5-8f11-c5e7b562a762}:f:\clinc\src\clinicmanagement.application\features\appointments\commands\
i:{a39610dd-838f-46d5-8f11-c5e7b562a762}:f:\clinc\src\clinicmanagement.application\features\medicalrecords\commands\
i:{a39610dd-838f-46d5-8f11-c5e7b562a762}:f:\clinc\src\clinicmanagement.application\features\patients\commands\
i:{a39610dd-838f-46d5-8f11-c5e7b562a762}:f:\clinc\src\clinicmanagement.application\features\schedules\commands\
++Queries
i:{a39610dd-838f-46d5-8f11-c5e7b562a762}:f:\clinc\src\clinicmanagement.application\features\appointments\queries\
i:{a39610dd-838f-46d5-8f11-c5e7b562a762}:f:\clinc\src\clinicmanagement.application\features\medicalrecords\queries\
i:{a39610dd-838f-46d5-8f11-c5e7b562a762}:f:\clinc\src\clinicmanagement.application\features\patients\queries\
i:{a39610dd-838f-46d5-8f11-c5e7b562a762}:f:\clinc\src\clinicmanagement.application\features\schedules\queries\
++MedicalRecords
i:{a39610dd-838f-46d5-8f11-c5e7b562a762}:f:\clinc\src\clinicmanagement.application\features\medicalrecords\
++Patients
i:{a39610dd-838f-46d5-8f11-c5e7b562a762}:f:\clinc\src\clinicmanagement.application\features\patients\
++Schedules
i:{a39610dd-838f-46d5-8f11-c5e7b562a762}:f:\clinc\src\clinicmanagement.application\features\schedules\
++ICommand.cs
i:{a39610dd-838f-46d5-8f11-c5e7b562a762}:f:\clinc\src\clinicmanagement.application\common\interfaces\icommand.cs
++ICurrentUserService.cs
i:{a39610dd-838f-46d5-8f11-c5e7b562a762}:f:\clinc\src\clinicmanagement.application\common\interfaces\icurrentuserservice.cs
++IDomainEventService.cs
i:{a39610dd-838f-46d5-8f11-c5e7b562a762}:f:\clinc\src\clinicmanagement.application\common\interfaces\idomaineventservice.cs
++IQuery.cs
i:{a39610dd-838f-46d5-8f11-c5e7b562a762}:f:\clinc\src\clinicmanagement.application\common\interfaces\iquery.cs
++CreateAppointment
i:{a39610dd-838f-46d5-8f11-c5e7b562a762}:f:\clinc\src\clinicmanagement.application\features\appointments\commands\createappointment\
++AppointmentDto.cs
i:{a39610dd-838f-46d5-8f11-c5e7b562a762}:f:\clinc\src\clinicmanagement.application\features\appointments\common\appointmentdto.cs
++GetAppointmentById
i:{a39610dd-838f-46d5-8f11-c5e7b562a762}:f:\clinc\src\clinicmanagement.application\features\appointments\queries\getappointmentbyid\
++GetAppointmentsByDateRange
i:{a39610dd-838f-46d5-8f11-c5e7b562a762}:f:\clinc\src\clinicmanagement.application\features\appointments\queries\getappointmentsbydaterange\
++CreateAppointmentCommand.cs
i:{a39610dd-838f-46d5-8f11-c5e7b562a762}:f:\clinc\src\clinicmanagement.application\features\appointments\commands\createappointment\createappointmentcommand.cs
++CreateAppointmentCommandHandler.cs
i:{a39610dd-838f-46d5-8f11-c5e7b562a762}:f:\clinc\src\clinicmanagement.application\features\appointments\commands\createappointment\createappointmentcommandhandler.cs
++CreateAppointmentCommandValidator.cs
i:{a39610dd-838f-46d5-8f11-c5e7b562a762}:f:\clinc\src\clinicmanagement.application\features\appointments\commands\createappointment\createappointmentcommandvalidator.cs
++GetAppointmentByIdQuery.cs
i:{a39610dd-838f-46d5-8f11-c5e7b562a762}:f:\clinc\src\clinicmanagement.application\features\appointments\queries\getappointmentbyid\getappointmentbyidquery.cs
++GetAppointmentsByDateRangeQuery.cs
i:{a39610dd-838f-46d5-8f11-c5e7b562a762}:f:\clinc\src\clinicmanagement.application\features\appointments\queries\getappointmentsbydaterange\getappointmentsbydaterangequery.cs
++CreateMedicalRecord
i:{a39610dd-838f-46d5-8f11-c5e7b562a762}:f:\clinc\src\clinicmanagement.application\features\medicalrecords\commands\createmedicalrecord\
++MedicalRecordDto.cs
i:{a39610dd-838f-46d5-8f11-c5e7b562a762}:f:\clinc\src\clinicmanagement.application\features\medicalrecords\common\medicalrecorddto.cs
++GetMedicalRecordById
i:{a39610dd-838f-46d5-8f11-c5e7b562a762}:f:\clinc\src\clinicmanagement.application\features\medicalrecords\queries\getmedicalrecordbyid\
++GetMedicalRecordsByPatient
i:{a39610dd-838f-46d5-8f11-c5e7b562a762}:f:\clinc\src\clinicmanagement.application\features\medicalrecords\queries\getmedicalrecordsbypatient\
++CreatePatient
i:{a39610dd-838f-46d5-8f11-c5e7b562a762}:f:\clinc\src\clinicmanagement.application\features\patients\commands\createpatient\
++PatientDto.cs
i:{a39610dd-838f-46d5-8f11-c5e7b562a762}:f:\clinc\src\clinicmanagement.application\features\patients\common\patientdto.cs
++GetPatientById
i:{a39610dd-838f-46d5-8f11-c5e7b562a762}:f:\clinc\src\clinicmanagement.application\features\patients\queries\getpatientbyid\
++GetPatientDetail
i:{a39610dd-838f-46d5-8f11-c5e7b562a762}:f:\clinc\src\clinicmanagement.application\features\patients\queries\getpatientdetail\
++GetPatientsList
i:{a39610dd-838f-46d5-8f11-c5e7b562a762}:f:\clinc\src\clinicmanagement.application\features\patients\queries\getpatientslist\
++CreateSchedule
i:{a39610dd-838f-46d5-8f11-c5e7b562a762}:f:\clinc\src\clinicmanagement.application\features\schedules\commands\createschedule\
++ScheduleDto.cs
i:{a39610dd-838f-46d5-8f11-c5e7b562a762}:f:\clinc\src\clinicmanagement.application\features\schedules\common\scheduledto.cs
++GetScheduleById
i:{a39610dd-838f-46d5-8f11-c5e7b562a762}:f:\clinc\src\clinicmanagement.application\features\schedules\queries\getschedulebyid\
++GetSchedulesByDoctor
i:{a39610dd-838f-46d5-8f11-c5e7b562a762}:f:\clinc\src\clinicmanagement.application\features\schedules\queries\getschedulesbydoctor\
++CreateMedicalRecordCommand.cs
i:{a39610dd-838f-46d5-8f11-c5e7b562a762}:f:\clinc\src\clinicmanagement.application\features\medicalrecords\commands\createmedicalrecord\createmedicalrecordcommand.cs
++CreateMedicalRecordCommandHandler.cs
i:{a39610dd-838f-46d5-8f11-c5e7b562a762}:f:\clinc\src\clinicmanagement.application\features\medicalrecords\commands\createmedicalrecord\createmedicalrecordcommandhandler.cs
++CreateMedicalRecordCommandValidator.cs
i:{a39610dd-838f-46d5-8f11-c5e7b562a762}:f:\clinc\src\clinicmanagement.application\features\medicalrecords\commands\createmedicalrecord\createmedicalrecordcommandvalidator.cs
++GetMedicalRecordByIdQuery.cs
i:{a39610dd-838f-46d5-8f11-c5e7b562a762}:f:\clinc\src\clinicmanagement.application\features\medicalrecords\queries\getmedicalrecordbyid\getmedicalrecordbyidquery.cs
++GetMedicalRecordsByPatientQuery.cs
i:{a39610dd-838f-46d5-8f11-c5e7b562a762}:f:\clinc\src\clinicmanagement.application\features\medicalrecords\queries\getmedicalrecordsbypatient\getmedicalrecordsbypatientquery.cs
++CreatePatientCommand.cs
i:{a39610dd-838f-46d5-8f11-c5e7b562a762}:f:\clinc\src\clinicmanagement.application\features\patients\commands\createpatient\createpatientcommand.cs
++CreatePatientCommandHandler.cs
i:{a39610dd-838f-46d5-8f11-c5e7b562a762}:f:\clinc\src\clinicmanagement.application\features\patients\commands\createpatient\createpatientcommandhandler.cs
++CreatePatientCommandValidator.cs
i:{a39610dd-838f-46d5-8f11-c5e7b562a762}:f:\clinc\src\clinicmanagement.application\features\patients\commands\createpatient\createpatientcommandvalidator.cs
++GetPatientByIdQuery.cs
i:{a39610dd-838f-46d5-8f11-c5e7b562a762}:f:\clinc\src\clinicmanagement.application\features\patients\queries\getpatientbyid\getpatientbyidquery.cs
++GetPatientDetailQuery.cs
i:{a39610dd-838f-46d5-8f11-c5e7b562a762}:f:\clinc\src\clinicmanagement.application\features\patients\queries\getpatientdetail\getpatientdetailquery.cs
++PatientDetailDto.cs
i:{a39610dd-838f-46d5-8f11-c5e7b562a762}:f:\clinc\src\clinicmanagement.application\features\patients\queries\getpatientdetail\patientdetaildto.cs
++GetPatientsListQuery.cs
i:{a39610dd-838f-46d5-8f11-c5e7b562a762}:f:\clinc\src\clinicmanagement.application\features\patients\queries\getpatientslist\getpatientslistquery.cs
++CreateScheduleCommand.cs
i:{a39610dd-838f-46d5-8f11-c5e7b562a762}:f:\clinc\src\clinicmanagement.application\features\schedules\commands\createschedule\createschedulecommand.cs
++CreateScheduleCommandHandler.cs
i:{a39610dd-838f-46d5-8f11-c5e7b562a762}:f:\clinc\src\clinicmanagement.application\features\schedules\commands\createschedule\createschedulecommandhandler.cs
++CreateScheduleCommandValidator.cs
i:{a39610dd-838f-46d5-8f11-c5e7b562a762}:f:\clinc\src\clinicmanagement.application\features\schedules\commands\createschedule\createschedulecommandvalidator.cs
++GetScheduleByIdQuery.cs
i:{a39610dd-838f-46d5-8f11-c5e7b562a762}:f:\clinc\src\clinicmanagement.application\features\schedules\queries\getschedulebyid\getschedulebyidquery.cs
++GetSchedulesByDoctorQuery.cs
i:{a39610dd-838f-46d5-8f11-c5e7b562a762}:f:\clinc\src\clinicmanagement.application\features\schedules\queries\getschedulesbydoctor\getschedulesbydoctorquery.cs
++User.cs
i:{94826b6e-2786-4c44-a29f-deaff0642f25}:f:\clinc\src\clinicmanagement.domain\entities\user.cs
++UserRole.cs
i:{94826b6e-2786-4c44-a29f-deaff0642f25}:f:\clinc\src\clinicmanagement.domain\enums\userrole.cs
++IUserRepository.cs
i:{94826b6e-2786-4c44-a29f-deaff0642f25}:f:\clinc\src\clinicmanagement.domain\repositories\iuserrepository.cs
++UserRepository.cs
i:{6594684e-1af1-4043-9dd5-d0c8ccceb2de}:f:\clinc\src\clinicmanagement.infrastructure\repositories\userrepository.cs
++IAuthService.cs
i:{a39610dd-838f-46d5-8f11-c5e7b562a762}:f:\clinc\src\clinicmanagement.application\common\interfaces\iauthservice.cs
++AuthService.cs
i:{6594684e-1af1-4043-9dd5-d0c8ccceb2de}:f:\clinc\src\clinicmanagement.infrastructure\services\authservice.cs
++AuthController.cs
i:{d0bb96d1-75ee-4922-b836-25f09b81cb55}:f:\clinc\src\clinicmanagement.webapi\controllers\authcontroller.cs
++Microsoft.EntityFrameworkCore.SqlServer (9.0.3)
i:{6594684e-1af1-4043-9dd5-d0c8ccceb2de}:>901
++BCrypt.Net-Next (4.0.3)
i:{6594684e-1af1-4043-9dd5-d0c8ccceb2de}:>943
++Microsoft.EntityFrameworkCore (9.0.3)
i:{6594684e-1af1-4043-9dd5-d0c8ccceb2de}:>942
++Microsoft.AspNetCore.Authentication.JwtBearer (9.0.5)
i:{d0bb96d1-75ee-4922-b836-25f09b81cb55}:>883
++Swashbuckle.AspNetCore (8.1.2)
i:{d0bb96d1-75ee-4922-b836-25f09b81cb55}:>881
++Microsoft.AspNetCore.OpenApi (9.0.5)
i:{d0bb96d1-75ee-4922-b836-25f09b81cb55}:>882
++Microsoft.EntityFrameworkCore.Tools (9.0.5)
i:{d0bb96d1-75ee-4922-b836-25f09b81cb55}:>944
++Microsoft.CodeAnalysis.Analyzers
i:{d0bb96d1-75ee-4922-b836-25f09b81cb55}:c:\users\<USER>\.nuget\packages\microsoft.codeanalysis.analyzers\3.3.4\analyzers\dotnet\cs\microsoft.codeanalysis.analyzers.dll
++Microsoft.CodeAnalysis.CSharp.Analyzers
i:{d0bb96d1-75ee-4922-b836-25f09b81cb55}:c:\users\<USER>\.nuget\packages\microsoft.codeanalysis.analyzers\3.3.4\analyzers\dotnet\cs\microsoft.codeanalysis.csharp.analyzers.dll
