import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { PatientService } from '../../../core/services/patient.service';
import { Gender, Patient, CreatePatientRequest, UpdatePatientRequest } from '../../../core/models/patient.model';

@Component({
  selector: 'app-patient-form',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, RouterModule],
  template: `
    <div class="min-h-screen bg-gray-50">
      <!-- Header Section -->
      <div class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div class="flex justify-between items-center py-6">
            <div>
              <h1 class="text-3xl font-bold text-gray-900">
                {{ isEditMode ? 'Edit Patient' : 'Add New Patient' }}
              </h1>
              <p class="mt-1 text-sm text-gray-500">
                {{ isEditMode ? 'Update patient information' : 'Enter patient details to create a new record' }}
              </p>
            </div>
            <button
              [routerLink]="['/patients']"
              class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200">
              <svg class="-ml-1 mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
              </svg>
              Back to Patients
            </button>
          </div>
        </div>
      </div>

      <!-- Main Content -->
      <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Loading State -->
        <div *ngIf="loading" class="bg-white rounded-lg shadow-sm border border-gray-200">
          <div class="flex justify-center items-center py-12">
            <div class="flex flex-col items-center">
              <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
              <p class="mt-4 text-sm text-gray-500">{{ isEditMode ? 'Loading patient data...' : 'Saving patient...' }}</p>
            </div>
          </div>
        </div>

        <!-- Error State -->
        <div *ngIf="error && !loading" class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
          <div class="flex">
            <div class="flex-shrink-0">
              <svg class="h-5 w-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
              </svg>
            </div>
            <div class="ml-3">
              <p class="text-sm text-red-800">{{ error }}</p>
            </div>
          </div>
        </div>

        <!-- Form -->
        <div *ngIf="!loading" class="bg-white rounded-lg shadow-sm border border-gray-200">
          <form [formGroup]="patientForm" (ngSubmit)="onSubmit()" class="space-y-6 p-6">
            <!-- Personal Information Section -->
            <div>
              <h3 class="text-lg font-medium text-gray-900 mb-4">Personal Information</h3>
              <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
                <!-- First Name -->
                <div>
                  <label for="firstName" class="block text-sm font-medium text-gray-700 mb-2">
                    First Name <span class="text-red-500">*</span>
                  </label>
                  <input
                    id="firstName"
                    type="text"
                    formControlName="firstName"
                    class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                    [class.border-red-300]="patientForm.get('firstName')?.invalid && patientForm.get('firstName')?.touched"
                    placeholder="Enter first name"
                  />
                  <div *ngIf="patientForm.get('firstName')?.invalid && patientForm.get('firstName')?.touched" class="mt-1 text-sm text-red-600">
                    First name is required
                  </div>
                </div>

                <!-- Last Name -->
                <div>
                  <label for="lastName" class="block text-sm font-medium text-gray-700 mb-2">
                    Last Name <span class="text-red-500">*</span>
                  </label>
                  <input
                    id="lastName"
                    type="text"
                    formControlName="lastName"
                    class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                    [class.border-red-300]="patientForm.get('lastName')?.invalid && patientForm.get('lastName')?.touched"
                    placeholder="Enter last name"
                  />
                  <div *ngIf="patientForm.get('lastName')?.invalid && patientForm.get('lastName')?.touched" class="mt-1 text-sm text-red-600">
                    Last name is required
                  </div>
                </div>

                <!-- Middle Name -->
                <div>
                  <label for="middleName" class="block text-sm font-medium text-gray-700 mb-2">
                    Middle Name
                  </label>
                  <input
                    id="middleName"
                    type="text"
                    formControlName="middleName"
                    class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                    placeholder="Enter middle name (optional)"
                  />
                </div>

                <!-- Date of Birth -->
                <div>
                  <label for="dateOfBirth" class="block text-sm font-medium text-gray-700 mb-2">
                    Date of Birth <span class="text-red-500">*</span>
                  </label>
                  <input
                    id="dateOfBirth"
                    type="date"
                    formControlName="dateOfBirth"
                    class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                    [class.border-red-300]="patientForm.get('dateOfBirth')?.invalid && patientForm.get('dateOfBirth')?.touched"
                  />
                  <div *ngIf="patientForm.get('dateOfBirth')?.invalid && patientForm.get('dateOfBirth')?.touched" class="mt-1 text-sm text-red-600">
                    Date of birth is required
                  </div>
                </div>

                <!-- Gender -->
                <div>
                  <label for="gender" class="block text-sm font-medium text-gray-700 mb-2">
                    Gender <span class="text-red-500">*</span>
                  </label>
                  <select
                    id="gender"
                    formControlName="gender"
                    class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                    [class.border-red-300]="patientForm.get('gender')?.invalid && patientForm.get('gender')?.touched"
                  >
                    <option value="">Select gender</option>
                    <option [value]="Gender.Male">Male</option>
                    <option [value]="Gender.Female">Female</option>
                    <option [value]="Gender.Other">Other</option>
                    <option [value]="Gender.PreferNotToSay">Prefer not to say</option>
                  </select>
                  <div *ngIf="patientForm.get('gender')?.invalid && patientForm.get('gender')?.touched" class="mt-1 text-sm text-red-600">
                    Gender is required
                  </div>
                </div>
              </div>
            </div>

            <!-- Contact Information Section -->
            <div class="border-t border-gray-200 pt-6">
              <h3 class="text-lg font-medium text-gray-900 mb-4">Contact Information</h3>
              <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
                <!-- Email -->
                <div>
                  <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                    Email Address <span class="text-red-500">*</span>
                  </label>
                  <input
                    id="email"
                    type="email"
                    formControlName="email"
                    class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                    [class.border-red-300]="patientForm.get('email')?.invalid && patientForm.get('email')?.touched"
                    placeholder="Enter email address"
                  />
                  <div *ngIf="patientForm.get('email')?.invalid && patientForm.get('email')?.touched" class="mt-1 text-sm text-red-600">
                    <span *ngIf="patientForm.get('email')?.errors?.['required']">Email is required</span>
                    <span *ngIf="patientForm.get('email')?.errors?.['email']">Please enter a valid email address</span>
                  </div>
                </div>

                <!-- Phone Number -->
                <div>
                  <label for="phoneNumber" class="block text-sm font-medium text-gray-700 mb-2">
                    Phone Number <span class="text-red-500">*</span>
                  </label>
                  <input
                    id="phoneNumber"
                    type="tel"
                    formControlName="phoneNumber"
                    class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                    [class.border-red-300]="patientForm.get('phoneNumber')?.invalid && patientForm.get('phoneNumber')?.touched"
                    placeholder="Enter phone number"
                  />
                  <div *ngIf="patientForm.get('phoneNumber')?.invalid && patientForm.get('phoneNumber')?.touched" class="mt-1 text-sm text-red-600">
                    Phone number is required
                  </div>
                </div>
              </div>
            </div>

            <!-- Address Information Section -->
            <div class="border-t border-gray-200 pt-6">
              <h3 class="text-lg font-medium text-gray-900 mb-4">Address Information</h3>
              <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
                <!-- Street -->
                <div class="sm:col-span-2">
                  <label for="street" class="block text-sm font-medium text-gray-700 mb-2">
                    Street Address <span class="text-red-500">*</span>
                  </label>
                  <input
                    id="street"
                    type="text"
                    formControlName="street"
                    class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                    [class.border-red-300]="patientForm.get('street')?.invalid && patientForm.get('street')?.touched"
                    placeholder="Enter street address"
                  />
                  <div *ngIf="patientForm.get('street')?.invalid && patientForm.get('street')?.touched" class="mt-1 text-sm text-red-600">
                    Street address is required
                  </div>
                </div>

                <!-- City -->
                <div>
                  <label for="city" class="block text-sm font-medium text-gray-700 mb-2">
                    City <span class="text-red-500">*</span>
                  </label>
                  <input
                    id="city"
                    type="text"
                    formControlName="city"
                    class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                    [class.border-red-300]="patientForm.get('city')?.invalid && patientForm.get('city')?.touched"
                    placeholder="Enter city"
                  />
                  <div *ngIf="patientForm.get('city')?.invalid && patientForm.get('city')?.touched" class="mt-1 text-sm text-red-600">
                    City is required
                  </div>
                </div>

                <!-- State -->
                <div>
                  <label for="state" class="block text-sm font-medium text-gray-700 mb-2">
                    State <span class="text-red-500">*</span>
                  </label>
                  <input
                    id="state"
                    type="text"
                    formControlName="state"
                    class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                    [class.border-red-300]="patientForm.get('state')?.invalid && patientForm.get('state')?.touched"
                    placeholder="Enter state"
                  />
                  <div *ngIf="patientForm.get('state')?.invalid && patientForm.get('state')?.touched" class="mt-1 text-sm text-red-600">
                    State is required
                  </div>
                </div>

                <!-- Postal Code -->
                <div>
                  <label for="postalCode" class="block text-sm font-medium text-gray-700 mb-2">
                    Postal Code <span class="text-red-500">*</span>
                  </label>
                  <input
                    id="postalCode"
                    type="text"
                    formControlName="postalCode"
                    class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                    [class.border-red-300]="patientForm.get('postalCode')?.invalid && patientForm.get('postalCode')?.touched"
                    placeholder="Enter postal code"
                  />
                  <div *ngIf="patientForm.get('postalCode')?.invalid && patientForm.get('postalCode')?.touched" class="mt-1 text-sm text-red-600">
                    Postal code is required
                  </div>
                </div>

                <!-- Country -->
                <div>
                  <label for="country" class="block text-sm font-medium text-gray-700 mb-2">
                    Country
                  </label>
                  <input
                    id="country"
                    type="text"
                    formControlName="country"
                    class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                    placeholder="Enter country"
                  />
                </div>
              </div>
            </div>

            <!-- Medical Information Section -->
            <div class="border-t border-gray-200 pt-6">
              <h3 class="text-lg font-medium text-gray-900 mb-4">Medical Information</h3>
              <div class="grid grid-cols-1 gap-6">
                <!-- Medical History -->
                <div>
                  <label for="medicalHistory" class="block text-sm font-medium text-gray-700 mb-2">
                    Medical History
                  </label>
                  <textarea
                    id="medicalHistory"
                    formControlName="medicalHistory"
                    rows="3"
                    class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                    placeholder="Enter medical history (optional)"
                  ></textarea>
                </div>

                <!-- Allergies -->
                <div>
                  <label for="allergies" class="block text-sm font-medium text-gray-700 mb-2">
                    Allergies
                  </label>
                  <textarea
                    id="allergies"
                    formControlName="allergies"
                    rows="2"
                    class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                    placeholder="Enter known allergies (optional)"
                  ></textarea>
                </div>
              </div>
            </div>

            <!-- Emergency Contact Section -->
            <div class="border-t border-gray-200 pt-6">
              <h3 class="text-lg font-medium text-gray-900 mb-4">Emergency Contact</h3>
              <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
                <!-- Emergency Contact Name -->
                <div>
                  <label for="emergencyContactName" class="block text-sm font-medium text-gray-700 mb-2">
                    Emergency Contact Name
                  </label>
                  <input
                    id="emergencyContactName"
                    type="text"
                    formControlName="emergencyContactName"
                    class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                    placeholder="Enter emergency contact name"
                  />
                </div>

                <!-- Emergency Contact Phone -->
                <div>
                  <label for="emergencyContactPhone" class="block text-sm font-medium text-gray-700 mb-2">
                    Emergency Contact Phone
                  </label>
                  <input
                    id="emergencyContactPhone"
                    type="tel"
                    formControlName="emergencyContactPhone"
                    class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                    placeholder="Enter emergency contact phone"
                  />
                </div>
              </div>
            </div>

            <!-- Insurance Information Section -->
            <div class="border-t border-gray-200 pt-6">
              <h3 class="text-lg font-medium text-gray-900 mb-4">Insurance Information</h3>
              <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
                <!-- Insurance Provider -->
                <div>
                  <label for="insuranceProvider" class="block text-sm font-medium text-gray-700 mb-2">
                    Insurance Provider
                  </label>
                  <input
                    id="insuranceProvider"
                    type="text"
                    formControlName="insuranceProvider"
                    class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                    placeholder="Enter insurance provider"
                  />
                </div>

                <!-- Insurance Policy Number -->
                <div>
                  <label for="insurancePolicyNumber" class="block text-sm font-medium text-gray-700 mb-2">
                    Policy Number
                  </label>
                  <input
                    id="insurancePolicyNumber"
                    type="text"
                    formControlName="insurancePolicyNumber"
                    class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                    placeholder="Enter policy number"
                  />
                </div>
              </div>
            </div>

            <!-- Form Actions -->
            <div class="border-t border-gray-200 pt-6">
              <div class="flex justify-end space-x-3">
                <button
                  type="button"
                  [routerLink]="['/patients']"
                  class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200">
                  Cancel
                </button>
                <button
                  type="submit"
                  [disabled]="patientForm.invalid || loading"
                  class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200">
                  <svg *ngIf="loading" class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  {{ isEditMode ? 'Update Patient' : 'Create Patient' }}
                </button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  `
})
export class PatientFormComponent implements OnInit {
  patientForm: FormGroup;
  isEditMode = false;
  patientId: number | null = null;
  loading = false;
  error: string | null = null;
  Gender = Gender; // Make Gender enum available in template

  constructor(
    private fb: FormBuilder,
    private patientService: PatientService,
    private route: ActivatedRoute,
    public router: Router // Make router public for template access
  ) {
    this.patientForm = this.fb.group({
      firstName: ['', Validators.required],
      lastName: ['', Validators.required],
      middleName: [''],
      email: ['', [Validators.required, Validators.email]],
      phoneNumber: ['', Validators.required],
      dateOfBirth: ['', Validators.required],
      gender: ['', Validators.required],
      street: ['', Validators.required],
      city: ['', Validators.required],
      state: ['', Validators.required],
      postalCode: ['', Validators.required],
      country: ['USA'],
      medicalHistory: [''],
      allergies: [''],
      emergencyContactName: [''],
      emergencyContactPhone: [''],
      insuranceProvider: [''],
      insurancePolicyNumber: ['']
    });
  }

  ngOnInit(): void {
    const idParam = this.route.snapshot.paramMap.get('id');
    if (idParam) {
      this.patientId = parseInt(idParam, 10);
      this.isEditMode = true;
      this.loadPatient();
    }
  }

  private loadPatient(): void {
    if (!this.patientId) return;

    this.loading = true;
    this.error = null;

    this.patientService.getById(this.patientId).subscribe({
      next: (patient: Patient) => {
        // Convert date to proper format for date input
        const dateOfBirth = patient.dateOfBirth ? new Date(patient.dateOfBirth).toISOString().split('T')[0] : '';

        this.patientForm.patchValue({
          firstName: patient.firstName,
          lastName: patient.lastName,
          middleName: patient.middleName || '',
          email: patient.email,
          phoneNumber: patient.phoneNumber,
          dateOfBirth: dateOfBirth,
          gender: patient.gender,
          street: patient.street,
          city: patient.city,
          state: patient.state,
          postalCode: patient.postalCode,
          country: patient.country || 'USA',
          medicalHistory: patient.medicalHistory || '',
          allergies: patient.allergies || '',
          emergencyContactName: patient.emergencyContactName || '',
          emergencyContactPhone: patient.emergencyContactPhone || '',
          insuranceProvider: patient.insuranceProvider || '',
          insurancePolicyNumber: patient.insurancePolicyNumber || ''
        });
        this.loading = false;
      },
      error: (error) => {
        this.error = 'Failed to load patient data. Please try again.';
        this.loading = false;
        console.error('Error loading patient:', error);
      }
    });
  }

  onSubmit(): void {
    if (this.patientForm.valid) {
      this.loading = true;
      this.error = null;

      const formData = this.patientForm.value;

      if (this.isEditMode && this.patientId) {
        // Update existing patient
        const updateData: UpdatePatientRequest = {
          id: this.patientId,
          ...formData
        };

        this.patientService.update(this.patientId, updateData).subscribe({
          next: () => {
            this.loading = false;
            this.router.navigate(['/patients']);
          },
          error: (error) => {
            this.error = 'Failed to update patient. Please try again.';
            this.loading = false;
            console.error('Error updating patient:', error);
          }
        });
      } else {
        // Create new patient
        const createData: CreatePatientRequest = formData;

        this.patientService.create(createData).subscribe({
          next: () => {
            this.loading = false;
            this.router.navigate(['/patients']);
          },
          error: (error) => {
            this.error = 'Failed to create patient. Please try again.';
            this.loading = false;
            console.error('Error creating patient:', error);
          }
        });
      }
    } else {
      // Mark all fields as touched to show validation errors
      Object.keys(this.patientForm.controls).forEach(key => {
        this.patientForm.get(key)?.markAsTouched();
      });
    }
  }
}