<div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
  <div class="max-w-md w-full space-y-8 bg-white p-8 rounded-lg shadow-md">
    <div class="text-center">
      <img class="mx-auto h-16 w-auto" src="assets/images/logo.png" alt="Clinic Logo">
      <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">Create an Account</h2>
      <p class="mt-2 text-center text-sm text-gray-600">
        Register to access the clinic management system
      </p>
    </div>
    
    <div *ngIf="error" class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative" role="alert">
      <span class="block sm:inline">{{ error }}</span>
    </div>

    <div *ngIf="success" class="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded relative" role="alert">
      <span class="block sm:inline">{{ success }}</span>
    </div>
    
    <form class="mt-8 space-y-6" [formGroup]="registerForm" (ngSubmit)="onSubmit()">
      <div class="rounded-md shadow-sm space-y-4">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label for="first-name" class="block text-sm font-medium text-gray-700">First Name</label>
            <input 
              id="first-name" 
              name="firstName" 
              type="text" 
              formControlName="firstName" 
              required 
              class="appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm" 
              placeholder="First Name"
              [ngClass]="{ 'border-red-500': submitted && f['firstName'].errors }"
            >
            <div *ngIf="submitted && f['firstName'].errors" class="text-red-500 text-xs mt-1">
              <div *ngIf="f['firstName'].errors['required']">First name is required</div>
            </div>
          </div>
          
          <div>
            <label for="last-name" class="block text-sm font-medium text-gray-700">Last Name</label>
            <input 
              id="last-name" 
              name="lastName" 
              type="text" 
              formControlName="lastName" 
              required 
              class="appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm" 
              placeholder="Last Name"
              [ngClass]="{ 'border-red-500': submitted && f['lastName'].errors }"
            >
            <div *ngIf="submitted && f['lastName'].errors" class="text-red-500 text-xs mt-1">
              <div *ngIf="f['lastName'].errors['required']">Last name is required</div>
            </div>
          </div>
        </div>
        
        <div>
          <label for="email-address" class="block text-sm font-medium text-gray-700">Email address</label>
          <input 
            id="email-address" 
            name="email" 
            type="email" 
            formControlName="email" 
            autocomplete="email" 
            required 
            class="appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm" 
            placeholder="Email address"
            [ngClass]="{ 'border-red-500': submitted && f['email'].errors }"
          >
          <div *ngIf="submitted && f['email'].errors" class="text-red-500 text-xs mt-1">
            <div *ngIf="f['email'].errors['required']">Email is required</div>
            <div *ngIf="f['email'].errors['email']">Email must be a valid email address</div>
          </div>
        </div>
        
        <div>
          <label for="password" class="block text-sm font-medium text-gray-700">Password</label>
          <input 
            id="password" 
            name="password" 
            type="password" 
            formControlName="password" 
            required 
            class="appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm" 
            placeholder="Password"
            [ngClass]="{ 'border-red-500': submitted && f['password'].errors }"
          >
          <div *ngIf="submitted && f['password'].errors" class="text-red-500 text-xs mt-1">
            <div *ngIf="f['password'].errors['required']">Password is required</div>
            <div *ngIf="f['password'].errors['minlength']">Password must be at least 6 characters</div>
          </div>
        </div>
        
        <div>
          <label for="confirm-password" class="block text-sm font-medium text-gray-700">Confirm Password</label>
          <input 
            id="confirm-password" 
            name="confirmPassword" 
            type="password" 
            formControlName="confirmPassword" 
            required 
            class="appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm" 
            placeholder="Confirm Password"
            [ngClass]="{ 'border-red-500': submitted && f['confirmPassword'].errors }"
          >
          <div *ngIf="submitted && f['confirmPassword'].errors" class="text-red-500 text-xs mt-1">
            <div *ngIf="f['confirmPassword'].errors['required']">Confirm password is required</div>
            <div *ngIf="f['confirmPassword'].errors['passwordMismatch']">Passwords do not match</div>
          </div>
        </div>
      </div>

      <div>
        <button 
          type="submit" 
          [disabled]="loading"
          class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"
        >
          <span *ngIf="loading" class="absolute left-0 inset-y-0 flex items-center pl-3">
            <svg class="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
          </span>
          <span *ngIf="!loading" class="absolute left-0 inset-y-0 flex items-center pl-3">
            <svg class="h-5 w-5 text-primary-500 group-hover:text-primary-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
              <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd" />
            </svg>
          </span>
          Register
        </button>
      </div>

      <div class="text-center mt-4">
        <p class="text-sm text-gray-600">
          Already have an account?
          <a routerLink="/login" class="font-medium text-primary-600 hover:text-primary-500">
            Sign in here
          </a>
        </p>
      </div>
    </form>
  </div>
</div> 