import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';

@Component({
  selector: 'app-medical-record-list',
  standalone: true,
  imports: [CommonModule, RouterModule],
  template: `
    <div class="container mx-auto p-4">
      <h1 class="text-2xl font-bold mb-4">Medical Records</h1>
      <div class="mb-4">
        <a routerLink="add" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
          Add New Record
        </a>
      </div>
      <div class="grid gap-4">
        <!-- Placeholder for medical records list -->
        <p>Medical records will be displayed here</p>
      </div>
    </div>
  `,
  styles: []
})
export class MedicalRecordListComponent implements OnInit {
  constructor() {}

  ngOnInit(): void {}
} 