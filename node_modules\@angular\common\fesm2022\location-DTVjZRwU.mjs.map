{"version": 3, "file": "location-DTVjZRwU.mjs", "sources": ["../../../../../darwin_arm64-fastbuild-ST-2d99d9656325/bin/packages/common/src/dom_adapter.ts", "../../../../../darwin_arm64-fastbuild-ST-2d99d9656325/bin/packages/common/src/location/platform_location.ts", "../../../../../darwin_arm64-fastbuild-ST-2d99d9656325/bin/packages/common/src/location/util.ts", "../../../../../darwin_arm64-fastbuild-ST-2d99d9656325/bin/packages/common/src/location/location_strategy.ts", "../../../../../darwin_arm64-fastbuild-ST-2d99d9656325/bin/packages/common/src/location/location.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nlet _DOM: DomAdapter = null!;\n\nexport function getDOM(): DomAdapter {\n  return _DOM;\n}\n\nexport function setRootDomAdapter(adapter: DomAdapter) {\n  _DOM ??= adapter;\n}\n\n/**\n * Provides DOM operations in an environment-agnostic way.\n *\n * @security Tread carefully! Interacting with the DOM directly is dangerous and\n * can introduce XSS risks.\n */\nexport abstract class DomAdapter {\n  // Needs Domino-friendly test utility\n  abstract dispatchEvent(el: any, evt: any): any;\n  abstract readonly supportsDOMEvents: boolean;\n\n  // Used by Meta\n  abstract remove(el: any): void;\n  abstract createElement(tagName: any, doc?: any): HTMLElement;\n  abstract createHtmlDocument(): Document;\n  abstract getDefaultDocument(): Document;\n\n  // Used by By.css\n  abstract isElementNode(node: any): boolean;\n\n  // Used by Testability\n  abstract isShadowRoot(node: any): boolean;\n\n  // Used by KeyEventsPlugin\n  abstract onAndCancel(el: any, evt: any, listener: any, options?: any): Function;\n\n  // Used by PlatformLocation and ServerEventManagerPlugin\n  abstract getGlobalEventTarget(doc: Document, target: string): any;\n\n  // Used by PlatformLocation\n  abstract getBaseHref(doc: Document): string | null;\n  abstract resetBaseElement(): void;\n\n  // TODO: remove dependency in DefaultValueAccessor\n  abstract getUserAgent(): string;\n\n  // Used in the legacy @angular/http package which has some usage in g3.\n  abstract getCookie(name: string): string | null;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {inject, Injectable, InjectionToken, DOCUMENT} from '@angular/core';\n\nimport {getDOM} from '../dom_adapter';\n\n/**\n * This class should not be used directly by an application developer. Instead, use\n * {@link Location}.\n *\n * `PlatformLocation` encapsulates all calls to DOM APIs, which allows the Router to be\n * platform-agnostic.\n * This means that we can have different implementation of `PlatformLocation` for the different\n * platforms that Angular supports. For example, `@angular/platform-browser` provides an\n * implementation specific to the browser environment, while `@angular/platform-server` provides\n * one suitable for use with server-side rendering.\n *\n * The `PlatformLocation` class is used directly by all implementations of {@link LocationStrategy}\n * when they need to interact with the DOM APIs like pushState, popState, etc.\n *\n * {@link LocationStrategy} in turn is used by the {@link Location} service which is used directly\n * by the {@link /api/router/Router Router} in order to navigate between routes. Since all interactions between\n * {@link /api/router/Router Router} /\n * {@link Location} / {@link LocationStrategy} and DOM APIs flow through the `PlatformLocation`\n * class, they are all platform-agnostic.\n *\n * @publicApi\n */\n@Injectable({providedIn: 'platform', useFactory: () => inject(BrowserPlatformLocation)})\nexport abstract class PlatformLocation {\n  abstract getBaseHrefFromDOM(): string;\n  abstract getState(): unknown;\n  /**\n   * Returns a function that, when executed, removes the `popstate` event handler.\n   */\n  abstract onPopState(fn: LocationChangeListener): VoidFunction;\n  /**\n   * Returns a function that, when executed, removes the `hashchange` event handler.\n   */\n  abstract onHashChange(fn: LocationChangeListener): VoidFunction;\n\n  abstract get href(): string;\n  abstract get protocol(): string;\n  abstract get hostname(): string;\n  abstract get port(): string;\n  abstract get pathname(): string;\n  abstract get search(): string;\n  abstract get hash(): string;\n\n  abstract replaceState(state: any, title: string, url: string): void;\n\n  abstract pushState(state: any, title: string, url: string): void;\n\n  abstract forward(): void;\n\n  abstract back(): void;\n\n  historyGo?(relativePosition: number): void {\n    throw new Error(ngDevMode ? 'Not implemented' : '');\n  }\n}\n\n/**\n * @description\n * Indicates when a location is initialized.\n *\n * @publicApi\n */\nexport const LOCATION_INITIALIZED = new InjectionToken<Promise<any>>(\n  ngDevMode ? 'Location Initialized' : '',\n);\n\n/**\n * @description\n * A serializable version of the event from `onPopState` or `onHashChange`\n *\n * @publicApi\n */\nexport interface LocationChangeEvent {\n  type: string;\n  state: any;\n}\n\n/**\n * @publicApi\n */\nexport interface LocationChangeListener {\n  (event: LocationChangeEvent): any;\n}\n\n/**\n * `PlatformLocation` encapsulates all of the direct calls to platform APIs.\n * This class should not be used directly by an application developer. Instead, use\n * {@link Location}.\n *\n * @publicApi\n */\n@Injectable({\n  providedIn: 'platform',\n  useFactory: () => new BrowserPlatformLocation(),\n})\nexport class BrowserPlatformLocation extends PlatformLocation {\n  private _location: Location;\n  private _history: History;\n  private _doc = inject(DOCUMENT);\n\n  constructor() {\n    super();\n    this._location = window.location;\n    this._history = window.history;\n  }\n\n  override getBaseHrefFromDOM(): string {\n    return getDOM().getBaseHref(this._doc)!;\n  }\n\n  override onPopState(fn: LocationChangeListener): VoidFunction {\n    const window = getDOM().getGlobalEventTarget(this._doc, 'window');\n    window.addEventListener('popstate', fn, false);\n    return () => window.removeEventListener('popstate', fn);\n  }\n\n  override onHashChange(fn: LocationChangeListener): VoidFunction {\n    const window = getDOM().getGlobalEventTarget(this._doc, 'window');\n    window.addEventListener('hashchange', fn, false);\n    return () => window.removeEventListener('hashchange', fn);\n  }\n\n  override get href(): string {\n    return this._location.href;\n  }\n  override get protocol(): string {\n    return this._location.protocol;\n  }\n  override get hostname(): string {\n    return this._location.hostname;\n  }\n  override get port(): string {\n    return this._location.port;\n  }\n  override get pathname(): string {\n    return this._location.pathname;\n  }\n  override get search(): string {\n    return this._location.search;\n  }\n  override get hash(): string {\n    return this._location.hash;\n  }\n  override set pathname(newPath: string) {\n    this._location.pathname = newPath;\n  }\n\n  override pushState(state: any, title: string, url: string): void {\n    this._history.pushState(state, title, url);\n  }\n\n  override replaceState(state: any, title: string, url: string): void {\n    this._history.replaceState(state, title, url);\n  }\n\n  override forward(): void {\n    this._history.forward();\n  }\n\n  override back(): void {\n    this._history.back();\n  }\n\n  override historyGo(relativePosition: number = 0): void {\n    this._history.go(relativePosition);\n  }\n\n  override getState(): unknown {\n    return this._history.state;\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n/**\n * Joins two parts of a URL with a slash if needed.\n *\n * @param start  URL string\n * @param end    URL string\n *\n *\n * @returns The joined URL string.\n */\nexport function joinWithSlash(start: string, end: string) {\n  // If `start` is an empty string, return `end` as the result.\n  if (!start) return end;\n  // If `end` is an empty string, return `start` as the result.\n  if (!end) return start;\n  // If `start` ends with a slash, remove the leading slash from `end`.\n  if (start.endsWith('/')) {\n    return end.startsWith('/') ? start + end.slice(1) : start + end;\n  }\n  // If `start` doesn't end with a slash, add one if `end` doesn't start with a slash.\n  return end.startsWith('/') ? start + end : `${start}/${end}`;\n}\n\n/**\n * Removes a trailing slash from a URL string if needed.\n * Looks for the first occurrence of either `#`, `?`, or the end of the\n * line as `/` characters and removes the trailing slash if one exists.\n *\n * @param url URL string.\n *\n * @returns The URL string, modified if needed.\n */\nexport function stripTrailingSlash(url: string): string {\n  // Find the index of the first occurrence of `#`, `?`, or the end of the string.\n  // This marks the start of the query string, fragment, or the end of the URL path.\n  const pathEndIdx = url.search(/#|\\?|$/);\n  // Check if the character before `pathEndIdx` is a trailing slash.\n  // If it is, remove the trailing slash and return the modified URL.\n  // Otherwise, return the URL as is.\n  return url[pathEndIdx - 1] === '/' ? url.slice(0, pathEndIdx - 1) + url.slice(pathEndIdx) : url;\n}\n\n/**\n * Normalizes URL parameters by prepending with `?` if needed.\n *\n * @param  params String of URL parameters.\n *\n * @returns The normalized URL parameters string.\n */\nexport function normalizeQueryParams(params: string): string {\n  return params && params[0] !== '?' ? `?${params}` : params;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  DOCUMENT,\n  Inject,\n  inject,\n  Injectable,\n  InjectionToken,\n  OnDestroy,\n  Optional,\n} from '@angular/core';\n\nimport {LocationChangeListener, PlatformLocation} from './platform_location';\nimport {joinWithSlash, normalizeQueryParams} from './util';\n\n/**\n * Enables the `Location` service to read route state from the browser's URL.\n * Angular provides two strategies:\n * `HashLocationStrategy` and `PathLocationStrategy`.\n *\n * Applications should use the `Router` or `Location` services to\n * interact with application route state.\n *\n * For instance, `HashLocationStrategy` produces URLs like\n * <code class=\"no-auto-link\">http://example.com/#/foo</code>,\n * and `PathLocationStrategy` produces\n * <code class=\"no-auto-link\">http://example.com/foo</code> as an equivalent URL.\n *\n * See these two classes for more.\n *\n * @publicApi\n */\n@Injectable({providedIn: 'root', useFactory: () => inject(PathLocationStrategy)})\nexport abstract class LocationStrategy {\n  abstract path(includeHash?: boolean): string;\n  abstract prepareExternalUrl(internal: string): string;\n  abstract getState(): unknown;\n  abstract pushState(state: any, title: string, url: string, queryParams: string): void;\n  abstract replaceState(state: any, title: string, url: string, queryParams: string): void;\n  abstract forward(): void;\n  abstract back(): void;\n  historyGo?(relativePosition: number): void {\n    throw new Error(ngDevMode ? 'Not implemented' : '');\n  }\n  abstract onPopState(fn: LocationChangeListener): void;\n  abstract getBaseHref(): string;\n}\n\n/**\n * A predefined DI token for the base href\n * to be used with the `PathLocationStrategy`.\n * The base href is the URL prefix that should be preserved when generating\n * and recognizing URLs.\n *\n * @usageNotes\n *\n * The following example shows how to use this token to configure the root app injector\n * with a base href value, so that the DI framework can supply the dependency anywhere in the app.\n *\n * ```ts\n * import {NgModule} from '@angular/core';\n * import {APP_BASE_HREF} from '@angular/common';\n *\n * @NgModule({\n *   providers: [{provide: APP_BASE_HREF, useValue: '/my/app'}]\n * })\n * class AppModule {}\n * ```\n *\n * @publicApi\n */\nexport const APP_BASE_HREF = new InjectionToken<string>(ngDevMode ? 'appBaseHref' : '');\n\n/**\n * @description\n * A {@link LocationStrategy} used to configure the {@link Location} service to\n * represent its state in the\n * [path](https://en.wikipedia.org/wiki/Uniform_Resource_Locator#Syntax) of the\n * browser's URL.\n *\n * If you're using `PathLocationStrategy`, you may provide a {@link APP_BASE_HREF}\n * or add a `<base href>` element to the document to override the default.\n *\n * For instance, if you provide an `APP_BASE_HREF` of `'/my/app/'` and call\n * `location.go('/foo')`, the browser's URL will become\n * `example.com/my/app/foo`. To ensure all relative URIs resolve correctly,\n * the `<base href>` and/or `APP_BASE_HREF` should end with a `/`.\n *\n * Similarly, if you add `<base href='/my/app/'/>` to the document and call\n * `location.go('/foo')`, the browser's URL will become\n * `example.com/my/app/foo`.\n *\n * Note that when using `PathLocationStrategy`, neither the query nor\n * the fragment in the `<base href>` will be preserved, as outlined\n * by the [RFC](https://tools.ietf.org/html/rfc3986#section-5.2.2).\n *\n * @usageNotes\n *\n * ### Example\n *\n * {@example common/location/ts/path_location_component.ts region='LocationComponent'}\n *\n * @publicApi\n */\n@Injectable({providedIn: 'root'})\nexport class PathLocationStrategy extends LocationStrategy implements OnDestroy {\n  private _baseHref: string;\n  private _removeListenerFns: (() => void)[] = [];\n\n  constructor(\n    private _platformLocation: PlatformLocation,\n    @Optional() @Inject(APP_BASE_HREF) href?: string,\n  ) {\n    super();\n\n    this._baseHref =\n      href ??\n      this._platformLocation.getBaseHrefFromDOM() ??\n      inject(DOCUMENT).location?.origin ??\n      '';\n  }\n\n  /** @docs-private */\n  ngOnDestroy(): void {\n    while (this._removeListenerFns.length) {\n      this._removeListenerFns.pop()!();\n    }\n  }\n\n  override onPopState(fn: LocationChangeListener): void {\n    this._removeListenerFns.push(\n      this._platformLocation.onPopState(fn),\n      this._platformLocation.onHashChange(fn),\n    );\n  }\n\n  override getBaseHref(): string {\n    return this._baseHref;\n  }\n\n  override prepareExternalUrl(internal: string): string {\n    return joinWithSlash(this._baseHref, internal);\n  }\n\n  override path(includeHash: boolean = false): string {\n    const pathname =\n      this._platformLocation.pathname + normalizeQueryParams(this._platformLocation.search);\n    const hash = this._platformLocation.hash;\n    return hash && includeHash ? `${pathname}${hash}` : pathname;\n  }\n\n  override pushState(state: any, title: string, url: string, queryParams: string) {\n    const externalUrl = this.prepareExternalUrl(url + normalizeQueryParams(queryParams));\n    this._platformLocation.pushState(state, title, externalUrl);\n  }\n\n  override replaceState(state: any, title: string, url: string, queryParams: string) {\n    const externalUrl = this.prepareExternalUrl(url + normalizeQueryParams(queryParams));\n    this._platformLocation.replaceState(state, title, externalUrl);\n  }\n\n  override forward(): void {\n    this._platformLocation.forward();\n  }\n\n  override back(): void {\n    this._platformLocation.back();\n  }\n\n  override getState(): unknown {\n    return this._platformLocation.getState();\n  }\n\n  override historyGo(relativePosition: number = 0): void {\n    this._platformLocation.historyGo?.(relativePosition);\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Injectable, OnDestroy, ɵɵinject} from '@angular/core';\nimport {Subject, SubscriptionLike} from 'rxjs';\n\nimport {LocationStrategy} from './location_strategy';\nimport {joinWithSlash, normalizeQueryParams, stripTrailingSlash} from './util';\n\n/** @publicApi */\nexport interface PopStateEvent {\n  pop?: boolean;\n  state?: any;\n  type?: string;\n  url?: string;\n}\n\n/**\n * @description\n *\n * A service that applications can use to interact with a browser's URL.\n *\n * Depending on the `LocationStrategy` used, `Location` persists\n * to the URL's path or the URL's hash segment.\n *\n * @usageNotes\n *\n * It's better to use the `Router.navigate()` service to trigger route changes. Use\n * `Location` only if you need to interact with or create normalized URLs outside of\n * routing.\n *\n * `Location` is responsible for normalizing the URL against the application's base href.\n * A normalized URL is absolute from the URL host, includes the application's base href, and has no\n * trailing slash:\n * - `/my/app/user/123` is normalized\n * - `my/app/user/123` **is not** normalized\n * - `/my/app/user/123/` **is not** normalized\n *\n * ### Example\n *\n * {@example common/location/ts/path_location_component.ts region='LocationComponent'}\n *\n * @publicApi\n */\n@Injectable({\n  providedIn: 'root',\n  // See #23917\n  useFactory: createLocation,\n})\nexport class Location implements OnDestroy {\n  /** @internal */\n  _subject = new Subject<PopStateEvent>();\n  /** @internal */\n  _basePath: string;\n  /** @internal */\n  _locationStrategy: LocationStrategy;\n  /** @internal */\n  _urlChangeListeners: ((url: string, state: unknown) => void)[] = [];\n  /** @internal */\n  _urlChangeSubscription: SubscriptionLike | null = null;\n\n  constructor(locationStrategy: LocationStrategy) {\n    this._locationStrategy = locationStrategy;\n    const baseHref = this._locationStrategy.getBaseHref();\n    // Note: This class's interaction with base HREF does not fully follow the rules\n    // outlined in the spec https://www.freesoft.org/CIE/RFC/1808/18.htm.\n    // Instead of trying to fix individual bugs with more and more code, we should\n    // investigate using the URL constructor and providing the base as a second\n    // argument.\n    // https://developer.mozilla.org/en-US/docs/Web/API/URL/URL#parameters\n    this._basePath = _stripOrigin(stripTrailingSlash(_stripIndexHtml(baseHref)));\n    this._locationStrategy.onPopState((ev) => {\n      this._subject.next({\n        'url': this.path(true),\n        'pop': true,\n        'state': ev.state,\n        'type': ev.type,\n      });\n    });\n  }\n\n  /** @docs-private */\n  ngOnDestroy(): void {\n    this._urlChangeSubscription?.unsubscribe();\n    this._urlChangeListeners = [];\n  }\n\n  /**\n   * Normalizes the URL path for this location.\n   *\n   * @param includeHash True to include an anchor fragment in the path.\n   *\n   * @returns The normalized URL path.\n   */\n  // TODO: vsavkin. Remove the boolean flag and always include hash once the deprecated router is\n  // removed.\n  path(includeHash: boolean = false): string {\n    return this.normalize(this._locationStrategy.path(includeHash));\n  }\n\n  /**\n   * Reports the current state of the location history.\n   * @returns The current value of the `history.state` object.\n   */\n  getState(): unknown {\n    return this._locationStrategy.getState();\n  }\n\n  /**\n   * Normalizes the given path and compares to the current normalized path.\n   *\n   * @param path The given URL path.\n   * @param query Query parameters.\n   *\n   * @returns True if the given URL path is equal to the current normalized path, false\n   * otherwise.\n   */\n  isCurrentPathEqualTo(path: string, query: string = ''): boolean {\n    return this.path() == this.normalize(path + normalizeQueryParams(query));\n  }\n\n  /**\n   * Normalizes a URL path by stripping any trailing slashes.\n   *\n   * @param url String representing a URL.\n   *\n   * @returns The normalized URL string.\n   */\n  normalize(url: string): string {\n    return Location.stripTrailingSlash(_stripBasePath(this._basePath, _stripIndexHtml(url)));\n  }\n\n  /**\n   * Normalizes an external URL path.\n   * If the given URL doesn't begin with a leading slash (`'/'`), adds one\n   * before normalizing. Adds a hash if `HashLocationStrategy` is\n   * in use, or the `APP_BASE_HREF` if the `PathLocationStrategy` is in use.\n   *\n   * @param url String representing a URL.\n   *\n   * @returns  A normalized platform-specific URL.\n   */\n  prepareExternalUrl(url: string): string {\n    if (url && url[0] !== '/') {\n      url = '/' + url;\n    }\n    return this._locationStrategy.prepareExternalUrl(url);\n  }\n\n  // TODO: rename this method to pushState\n  /**\n   * Changes the browser's URL to a normalized version of a given URL, and pushes a\n   * new item onto the platform's history.\n   *\n   * @param path  URL path to normalize.\n   * @param query Query parameters.\n   * @param state Location history state.\n   *\n   */\n  go(path: string, query: string = '', state: any = null): void {\n    this._locationStrategy.pushState(state, '', path, query);\n    this._notifyUrlChangeListeners(\n      this.prepareExternalUrl(path + normalizeQueryParams(query)),\n      state,\n    );\n  }\n\n  /**\n   * Changes the browser's URL to a normalized version of the given URL, and replaces\n   * the top item on the platform's history stack.\n   *\n   * @param path  URL path to normalize.\n   * @param query Query parameters.\n   * @param state Location history state.\n   */\n  replaceState(path: string, query: string = '', state: any = null): void {\n    this._locationStrategy.replaceState(state, '', path, query);\n    this._notifyUrlChangeListeners(\n      this.prepareExternalUrl(path + normalizeQueryParams(query)),\n      state,\n    );\n  }\n\n  /**\n   * Navigates forward in the platform's history.\n   */\n  forward(): void {\n    this._locationStrategy.forward();\n  }\n\n  /**\n   * Navigates back in the platform's history.\n   */\n  back(): void {\n    this._locationStrategy.back();\n  }\n\n  /**\n   * Navigate to a specific page from session history, identified by its relative position to the\n   * current page.\n   *\n   * @param relativePosition  Position of the target page in the history relative to the current\n   *     page.\n   * A negative value moves backwards, a positive value moves forwards, e.g. `location.historyGo(2)`\n   * moves forward two pages and `location.historyGo(-2)` moves back two pages. When we try to go\n   * beyond what's stored in the history session, we stay in the current page. Same behaviour occurs\n   * when `relativePosition` equals 0.\n   * @see https://developer.mozilla.org/en-US/docs/Web/API/History_API#Moving_to_a_specific_point_in_history\n   */\n  historyGo(relativePosition: number = 0): void {\n    this._locationStrategy.historyGo?.(relativePosition);\n  }\n\n  /**\n   * Registers a URL change listener. Use to catch updates performed by the Angular\n   * framework that are not detectible through \"popstate\" or \"hashchange\" events.\n   *\n   * @param fn The change handler function, which take a URL and a location history state.\n   * @returns A function that, when executed, unregisters a URL change listener.\n   */\n  onUrlChange(fn: (url: string, state: unknown) => void): VoidFunction {\n    this._urlChangeListeners.push(fn);\n\n    this._urlChangeSubscription ??= this.subscribe((v) => {\n      this._notifyUrlChangeListeners(v.url, v.state);\n    });\n\n    return () => {\n      const fnIndex = this._urlChangeListeners.indexOf(fn);\n      this._urlChangeListeners.splice(fnIndex, 1);\n\n      if (this._urlChangeListeners.length === 0) {\n        this._urlChangeSubscription?.unsubscribe();\n        this._urlChangeSubscription = null;\n      }\n    };\n  }\n\n  /** @internal */\n  _notifyUrlChangeListeners(url: string = '', state: unknown) {\n    this._urlChangeListeners.forEach((fn) => fn(url, state));\n  }\n\n  /**\n   * Subscribes to the platform's `popState` events.\n   *\n   * Note: `Location.go()` does not trigger the `popState` event in the browser. Use\n   * `Location.onUrlChange()` to subscribe to URL changes instead.\n   *\n   * @param value Event that is triggered when the state history changes.\n   * @param exception The exception to throw.\n   *\n   * @see [onpopstate](https://developer.mozilla.org/en-US/docs/Web/API/WindowEventHandlers/onpopstate)\n   *\n   * @returns Subscribed events.\n   */\n  subscribe(\n    onNext: (value: PopStateEvent) => void,\n    onThrow?: ((exception: any) => void) | null,\n    onReturn?: (() => void) | null,\n  ): SubscriptionLike {\n    return this._subject.subscribe({\n      next: onNext,\n      error: onThrow ?? undefined,\n      complete: onReturn ?? undefined,\n    });\n  }\n\n  /**\n   * Normalizes URL parameters by prepending with `?` if needed.\n   *\n   * @param  params String of URL parameters.\n   *\n   * @returns The normalized URL parameters string.\n   */\n  public static normalizeQueryParams: (params: string) => string = normalizeQueryParams;\n\n  /**\n   * Joins two parts of a URL with a slash if needed.\n   *\n   * @param start  URL string\n   * @param end    URL string\n   *\n   *\n   * @returns The joined URL string.\n   */\n  public static joinWithSlash: (start: string, end: string) => string = joinWithSlash;\n\n  /**\n   * Removes a trailing slash from a URL string if needed.\n   * Looks for the first occurrence of either `#`, `?`, or the end of the\n   * line as `/` characters and removes the trailing slash if one exists.\n   *\n   * @param url URL string.\n   *\n   * @returns The URL string, modified if needed.\n   */\n  public static stripTrailingSlash: (url: string) => string = stripTrailingSlash;\n}\n\nexport function createLocation() {\n  return new Location(ɵɵinject(LocationStrategy as any));\n}\n\nfunction _stripBasePath(basePath: string, url: string): string {\n  if (!basePath || !url.startsWith(basePath)) {\n    return url;\n  }\n  const strippedUrl = url.substring(basePath.length);\n  if (strippedUrl === '' || ['/', ';', '?', '#'].includes(strippedUrl[0])) {\n    return strippedUrl;\n  }\n  return url;\n}\n\nfunction _stripIndexHtml(url: string): string {\n  return url.replace(/\\/index.html$/, '');\n}\n\nfunction _stripOrigin(baseHref: string): string {\n  // DO NOT REFACTOR! Previously, this check looked like this:\n  // `/^(https?:)?\\/\\//.test(baseHref)`, but that resulted in\n  // syntactically incorrect code after Closure Compiler minification.\n  // This was likely caused by a bug in Closure Compiler, but\n  // for now, the check is rewritten to use `new RegExp` instead.\n  const isAbsoluteUrl = new RegExp('^(https?:)?//').test(baseHref);\n  if (isAbsoluteUrl) {\n    const [, pathname] = baseHref.split(/\\/\\/[^\\/]+/);\n    return pathname;\n  }\n  return baseHref;\n}\n"], "names": ["i1.LocationStrategy", "ɵɵinject"], "mappings": ";;;;;;;;;;AAQA,IAAI,IAAI,GAAe,IAAK;SAEZ,MAAM,GAAA;AACpB,IAAA,OAAO,IAAI;AACb;AAEM,SAAU,iBAAiB,CAAC,OAAmB,EAAA;IACnD,IAAI,KAAK,OAAO;AAClB;AAEA;;;;;AAKG;MACmB,UAAU,CAAA;AAgC/B;;AC5CD;;;;;;;;;;;;;;;;;;;;;AAqBG;MAEmB,gBAAgB,CAAA;AA4BpC,IAAA,SAAS,CAAE,gBAAwB,EAAA;AACjC,QAAA,MAAM,IAAI,KAAK,CAAC,SAAS,GAAG,iBAAiB,GAAG,EAAE,CAAC;;kHA7BjC,gBAAgB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA;sHAAhB,gBAAgB,EAAA,UAAA,EADb,UAAU,EAAc,UAAA,EAAA,MAAM,MAAM,CAAC,uBAAuB,CAAC,EAAA,CAAA;;sGAChE,gBAAgB,EAAA,UAAA,EAAA,CAAA;kBADrC,UAAU;AAAC,YAAA,IAAA,EAAA,CAAA,EAAC,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,MAAM,MAAM,CAAC,uBAAuB,CAAC,EAAC;;AAkCvF;;;;;AAKG;AACU,MAAA,oBAAoB,GAAG,IAAI,cAAc,CACpD,SAAS,GAAG,sBAAsB,GAAG,EAAE;AAqBzC;;;;;;AAMG;AAKG,MAAO,uBAAwB,SAAQ,gBAAgB,CAAA;AACnD,IAAA,SAAS;AACT,IAAA,QAAQ;AACR,IAAA,IAAI,GAAG,MAAM,CAAC,QAAQ,CAAC;AAE/B,IAAA,WAAA,GAAA;AACE,QAAA,KAAK,EAAE;AACP,QAAA,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC,QAAQ;AAChC,QAAA,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAC,OAAO;;IAGvB,kBAAkB,GAAA;QACzB,OAAO,MAAM,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAE;;AAGhC,IAAA,UAAU,CAAC,EAA0B,EAAA;AAC5C,QAAA,MAAM,MAAM,GAAG,MAAM,EAAE,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC;QACjE,MAAM,CAAC,gBAAgB,CAAC,UAAU,EAAE,EAAE,EAAE,KAAK,CAAC;QAC9C,OAAO,MAAM,MAAM,CAAC,mBAAmB,CAAC,UAAU,EAAE,EAAE,CAAC;;AAGhD,IAAA,YAAY,CAAC,EAA0B,EAAA;AAC9C,QAAA,MAAM,MAAM,GAAG,MAAM,EAAE,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC;QACjE,MAAM,CAAC,gBAAgB,CAAC,YAAY,EAAE,EAAE,EAAE,KAAK,CAAC;QAChD,OAAO,MAAM,MAAM,CAAC,mBAAmB,CAAC,YAAY,EAAE,EAAE,CAAC;;AAG3D,IAAA,IAAa,IAAI,GAAA;AACf,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI;;AAE5B,IAAA,IAAa,QAAQ,GAAA;AACnB,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ;;AAEhC,IAAA,IAAa,QAAQ,GAAA;AACnB,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ;;AAEhC,IAAA,IAAa,IAAI,GAAA;AACf,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI;;AAE5B,IAAA,IAAa,QAAQ,GAAA;AACnB,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ;;AAEhC,IAAA,IAAa,MAAM,GAAA;AACjB,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM;;AAE9B,IAAA,IAAa,IAAI,GAAA;AACf,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI;;IAE5B,IAAa,QAAQ,CAAC,OAAe,EAAA;AACnC,QAAA,IAAI,CAAC,SAAS,CAAC,QAAQ,GAAG,OAAO;;AAG1B,IAAA,SAAS,CAAC,KAAU,EAAE,KAAa,EAAE,GAAW,EAAA;QACvD,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,KAAK,EAAE,KAAK,EAAE,GAAG,CAAC;;AAGnC,IAAA,YAAY,CAAC,KAAU,EAAE,KAAa,EAAE,GAAW,EAAA;QAC1D,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,KAAK,EAAE,KAAK,EAAE,GAAG,CAAC;;IAGtC,OAAO,GAAA;AACd,QAAA,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE;;IAGhB,IAAI,GAAA;AACX,QAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE;;IAGb,SAAS,CAAC,mBAA2B,CAAC,EAAA;AAC7C,QAAA,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,gBAAgB,CAAC;;IAG3B,QAAQ,GAAA;AACf,QAAA,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK;;kHAzEjB,uBAAuB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA;sHAAvB,uBAAuB,EAAA,UAAA,EAHtB,UAAU,EACV,UAAA,EAAA,MAAM,IAAI,uBAAuB,EAAE,EAAA,CAAA;;sGAEpC,uBAAuB,EAAA,UAAA,EAAA,CAAA;kBAJnC,UAAU;AAAC,YAAA,IAAA,EAAA,CAAA;AACV,oBAAA,UAAU,EAAE,UAAU;AACtB,oBAAA,UAAU,EAAE,MAAM,IAA6B,uBAAA,EAAA;AAChD,iBAAA;;;AClGD;;;;;;;;AAQG;AACa,SAAA,aAAa,CAAC,KAAa,EAAE,GAAW,EAAA;;AAEtD,IAAA,IAAI,CAAC,KAAK;AAAE,QAAA,OAAO,GAAG;;AAEtB,IAAA,IAAI,CAAC,GAAG;AAAE,QAAA,OAAO,KAAK;;AAEtB,IAAA,IAAI,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;QACvB,OAAO,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,GAAG,GAAG;;;IAGjE,OAAO,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,KAAK,GAAG,GAAG,GAAG,CAAA,EAAG,KAAK,CAAI,CAAA,EAAA,GAAG,EAAE;AAC9D;AAEA;;;;;;;;AAQG;AACG,SAAU,kBAAkB,CAAC,GAAW,EAAA;;;IAG5C,MAAM,UAAU,GAAG,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAC;;;;AAIvC,IAAA,OAAO,GAAG,CAAC,UAAU,GAAG,CAAC,CAAC,KAAK,GAAG,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,UAAU,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,KAAK,CAAC,UAAU,CAAC,GAAG,GAAG;AACjG;AAEA;;;;;;AAMG;AACG,SAAU,oBAAoB,CAAC,MAAc,EAAA;AACjD,IAAA,OAAO,MAAM,IAAI,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,GAAG,IAAI,MAAM,CAAA,CAAE,GAAG,MAAM;AAC5D;;ACrCA;;;;;;;;;;;;;;;;AAgBG;MAEmB,gBAAgB,CAAA;AAQpC,IAAA,SAAS,CAAE,gBAAwB,EAAA;AACjC,QAAA,MAAM,IAAI,KAAK,CAAC,SAAS,GAAG,iBAAiB,GAAG,EAAE,CAAC;;kHATjC,gBAAgB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA;sHAAhB,gBAAgB,EAAA,UAAA,EADb,MAAM,EAAc,UAAA,EAAA,MAAM,MAAM,CAAC,oBAAoB,CAAC,EAAA,CAAA;;sGACzD,gBAAgB,EAAA,UAAA,EAAA,CAAA;kBADrC,UAAU;AAAC,YAAA,IAAA,EAAA,CAAA,EAAC,UAAU,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,MAAM,CAAC,oBAAoB,CAAC,EAAC;;AAgBhF;;;;;;;;;;;;;;;;;;;;;;AAsBG;AACU,MAAA,aAAa,GAAG,IAAI,cAAc,CAAS,SAAS,GAAG,aAAa,GAAG,EAAE;AAEtF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8BG;AAEG,MAAO,oBAAqB,SAAQ,gBAAgB,CAAA;AAK9C,IAAA,iBAAA;AAJF,IAAA,SAAS;IACT,kBAAkB,GAAmB,EAAE;IAE/C,WACU,CAAA,iBAAmC,EACR,IAAa,EAAA;AAEhD,QAAA,KAAK,EAAE;QAHC,IAAiB,CAAA,iBAAA,GAAjB,iBAAiB;AAKzB,QAAA,IAAI,CAAC,SAAS;YACZ,IAAI;AACJ,gBAAA,IAAI,CAAC,iBAAiB,CAAC,kBAAkB,EAAE;AAC3C,gBAAA,MAAM,CAAC,QAAQ,CAAC,CAAC,QAAQ,EAAE,MAAM;AACjC,gBAAA,EAAE;;;IAIN,WAAW,GAAA;AACT,QAAA,OAAO,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE;AACrC,YAAA,IAAI,CAAC,kBAAkB,CAAC,GAAG,EAAG,EAAE;;;AAI3B,IAAA,UAAU,CAAC,EAA0B,EAAA;QAC5C,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAC1B,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,EAAE,CAAC,EACrC,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC,EAAE,CAAC,CACxC;;IAGM,WAAW,GAAA;QAClB,OAAO,IAAI,CAAC,SAAS;;AAGd,IAAA,kBAAkB,CAAC,QAAgB,EAAA;QAC1C,OAAO,aAAa,CAAC,IAAI,CAAC,SAAS,EAAE,QAAQ,CAAC;;IAGvC,IAAI,CAAC,cAAuB,KAAK,EAAA;AACxC,QAAA,MAAM,QAAQ,GACZ,IAAI,CAAC,iBAAiB,CAAC,QAAQ,GAAG,oBAAoB,CAAC,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC;AACvF,QAAA,MAAM,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI;AACxC,QAAA,OAAO,IAAI,IAAI,WAAW,GAAG,CAAA,EAAG,QAAQ,CAAA,EAAG,IAAI,CAAE,CAAA,GAAG,QAAQ;;AAGrD,IAAA,SAAS,CAAC,KAAU,EAAE,KAAa,EAAE,GAAW,EAAE,WAAmB,EAAA;AAC5E,QAAA,MAAM,WAAW,GAAG,IAAI,CAAC,kBAAkB,CAAC,GAAG,GAAG,oBAAoB,CAAC,WAAW,CAAC,CAAC;QACpF,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,KAAK,EAAE,KAAK,EAAE,WAAW,CAAC;;AAGpD,IAAA,YAAY,CAAC,KAAU,EAAE,KAAa,EAAE,GAAW,EAAE,WAAmB,EAAA;AAC/E,QAAA,MAAM,WAAW,GAAG,IAAI,CAAC,kBAAkB,CAAC,GAAG,GAAG,oBAAoB,CAAC,WAAW,CAAC,CAAC;QACpF,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC,KAAK,EAAE,KAAK,EAAE,WAAW,CAAC;;IAGvD,OAAO,GAAA;AACd,QAAA,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE;;IAGzB,IAAI,GAAA;AACX,QAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE;;IAGtB,QAAQ,GAAA;AACf,QAAA,OAAO,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE;;IAGjC,SAAS,CAAC,mBAA2B,CAAC,EAAA;QAC7C,IAAI,CAAC,iBAAiB,CAAC,SAAS,GAAG,gBAAgB,CAAC;;AArE3C,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,oBAAoB,+CAMT,aAAa,EAAA,QAAA,EAAA,IAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA;AANxB,IAAA,OAAA,KAAA,GAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,oBAAoB,cADR,MAAM,EAAA,CAAA;;sGAClB,oBAAoB,EAAA,UAAA,EAAA,CAAA;kBADhC,UAAU;mBAAC,EAAC,UAAU,EAAE,MAAM,EAAC;;0BAO3B;;0BAAY,MAAM;2BAAC,aAAa;;;AC/FrC;;;;;;;;;;;;;;;;;;;;;;;;;;AA0BG;MAMU,QAAQ,CAAA;;AAEnB,IAAA,QAAQ,GAAG,IAAI,OAAO,EAAiB;;AAEvC,IAAA,SAAS;;AAET,IAAA,iBAAiB;;IAEjB,mBAAmB,GAA8C,EAAE;;IAEnE,sBAAsB,GAA4B,IAAI;AAEtD,IAAA,WAAA,CAAY,gBAAkC,EAAA;AAC5C,QAAA,IAAI,CAAC,iBAAiB,GAAG,gBAAgB;QACzC,MAAM,QAAQ,GAAG,IAAI,CAAC,iBAAiB,CAAC,WAAW,EAAE;;;;;;;AAOrD,QAAA,IAAI,CAAC,SAAS,GAAG,YAAY,CAAC,kBAAkB,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC,CAAC;QAC5E,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC,EAAE,KAAI;AACvC,YAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;AACjB,gBAAA,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;AACtB,gBAAA,KAAK,EAAE,IAAI;gBACX,OAAO,EAAE,EAAE,CAAC,KAAK;gBACjB,MAAM,EAAE,EAAE,CAAC,IAAI;AAChB,aAAA,CAAC;AACJ,SAAC,CAAC;;;IAIJ,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,sBAAsB,EAAE,WAAW,EAAE;AAC1C,QAAA,IAAI,CAAC,mBAAmB,GAAG,EAAE;;AAG/B;;;;;;AAMG;;;IAGH,IAAI,CAAC,cAAuB,KAAK,EAAA;AAC/B,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;;AAGjE;;;AAGG;IACH,QAAQ,GAAA;AACN,QAAA,OAAO,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE;;AAG1C;;;;;;;;AAQG;AACH,IAAA,oBAAoB,CAAC,IAAY,EAAE,KAAA,GAAgB,EAAE,EAAA;AACnD,QAAA,OAAO,IAAI,CAAC,IAAI,EAAE,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,GAAG,oBAAoB,CAAC,KAAK,CAAC,CAAC;;AAG1E;;;;;;AAMG;AACH,IAAA,SAAS,CAAC,GAAW,EAAA;AACnB,QAAA,OAAO,QAAQ,CAAC,kBAAkB,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS,EAAE,eAAe,CAAC,GAAG,CAAC,CAAC,CAAC;;AAG1F;;;;;;;;;AASG;AACH,IAAA,kBAAkB,CAAC,GAAW,EAAA;QAC5B,IAAI,GAAG,IAAI,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;AACzB,YAAA,GAAG,GAAG,GAAG,GAAG,GAAG;;QAEjB,OAAO,IAAI,CAAC,iBAAiB,CAAC,kBAAkB,CAAC,GAAG,CAAC;;;AAIvD;;;;;;;;AAQG;AACH,IAAA,EAAE,CAAC,IAAY,EAAE,QAAgB,EAAE,EAAE,QAAa,IAAI,EAAA;AACpD,QAAA,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,KAAK,EAAE,EAAE,EAAE,IAAI,EAAE,KAAK,CAAC;AACxD,QAAA,IAAI,CAAC,yBAAyB,CAC5B,IAAI,CAAC,kBAAkB,CAAC,IAAI,GAAG,oBAAoB,CAAC,KAAK,CAAC,CAAC,EAC3D,KAAK,CACN;;AAGH;;;;;;;AAOG;AACH,IAAA,YAAY,CAAC,IAAY,EAAE,QAAgB,EAAE,EAAE,QAAa,IAAI,EAAA;AAC9D,QAAA,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC,KAAK,EAAE,EAAE,EAAE,IAAI,EAAE,KAAK,CAAC;AAC3D,QAAA,IAAI,CAAC,yBAAyB,CAC5B,IAAI,CAAC,kBAAkB,CAAC,IAAI,GAAG,oBAAoB,CAAC,KAAK,CAAC,CAAC,EAC3D,KAAK,CACN;;AAGH;;AAEG;IACH,OAAO,GAAA;AACL,QAAA,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE;;AAGlC;;AAEG;IACH,IAAI,GAAA;AACF,QAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE;;AAG/B;;;;;;;;;;;AAWG;IACH,SAAS,CAAC,mBAA2B,CAAC,EAAA;QACpC,IAAI,CAAC,iBAAiB,CAAC,SAAS,GAAG,gBAAgB,CAAC;;AAGtD;;;;;;AAMG;AACH,IAAA,WAAW,CAAC,EAAyC,EAAA;AACnD,QAAA,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,EAAE,CAAC;QAEjC,IAAI,CAAC,sBAAsB,KAAK,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,KAAI;YACnD,IAAI,CAAC,yBAAyB,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,KAAK,CAAC;AAChD,SAAC,CAAC;AAEF,QAAA,OAAO,MAAK;YACV,MAAM,OAAO,GAAG,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,EAAE,CAAC;YACpD,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC;YAE3C,IAAI,IAAI,CAAC,mBAAmB,CAAC,MAAM,KAAK,CAAC,EAAE;AACzC,gBAAA,IAAI,CAAC,sBAAsB,EAAE,WAAW,EAAE;AAC1C,gBAAA,IAAI,CAAC,sBAAsB,GAAG,IAAI;;AAEtC,SAAC;;;AAIH,IAAA,yBAAyB,CAAC,GAAA,GAAc,EAAE,EAAE,KAAc,EAAA;AACxD,QAAA,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;;AAG1D;;;;;;;;;;;;AAYG;AACH,IAAA,SAAS,CACP,MAAsC,EACtC,OAA2C,EAC3C,QAA8B,EAAA;AAE9B,QAAA,OAAO,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC;AAC7B,YAAA,IAAI,EAAE,MAAM;YACZ,KAAK,EAAE,OAAO,IAAI,SAAS;YAC3B,QAAQ,EAAE,QAAQ,IAAI,SAAS;AAChC,SAAA,CAAC;;AAGJ;;;;;;AAMG;AACI,IAAA,OAAO,oBAAoB,GAA+B,oBAAoB;AAErF;;;;;;;;AAQG;AACI,IAAA,OAAO,aAAa,GAA2C,aAAa;AAEnF;;;;;;;;AAQG;AACI,IAAA,OAAO,kBAAkB,GAA4B,kBAAkB;kHAxPnE,QAAQ,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAAA,gBAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA;sHAAR,QAAQ,EAAA,UAAA,EAJP,MAAM,EAAA,UAAA,EAEN,cAAc,EAAA,CAAA;;sGAEf,QAAQ,EAAA,UAAA,EAAA,CAAA;kBALpB,UAAU;AAAC,YAAA,IAAA,EAAA,CAAA;AACV,oBAAA,UAAU,EAAE,MAAM;;AAElB,oBAAA,UAAU,EAAE,cAAc;AAC3B,iBAAA;;SA4Pe,cAAc,GAAA;IAC5B,OAAO,IAAI,QAAQ,CAACC,QAAQ,CAAC,gBAAuB,CAAC,CAAC;AACxD;AAEA,SAAS,cAAc,CAAC,QAAgB,EAAE,GAAW,EAAA;IACnD,IAAI,CAAC,QAAQ,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE;AAC1C,QAAA,OAAO,GAAG;;IAEZ,MAAM,WAAW,GAAG,GAAG,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC;IAClD,IAAI,WAAW,KAAK,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE;AACvE,QAAA,OAAO,WAAW;;AAEpB,IAAA,OAAO,GAAG;AACZ;AAEA,SAAS,eAAe,CAAC,GAAW,EAAA;IAClC,OAAO,GAAG,CAAC,OAAO,CAAC,eAAe,EAAE,EAAE,CAAC;AACzC;AAEA,SAAS,YAAY,CAAC,QAAgB,EAAA;;;;;;AAMpC,IAAA,MAAM,aAAa,GAAG,IAAI,MAAM,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC;IAChE,IAAI,aAAa,EAAE;QACjB,MAAM,GAAG,QAAQ,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC,YAAY,CAAC;AACjD,QAAA,OAAO,QAAQ;;AAEjB,IAAA,OAAO,QAAQ;AACjB;;;;"}