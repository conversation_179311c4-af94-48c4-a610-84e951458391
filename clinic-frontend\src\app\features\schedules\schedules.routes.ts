import { Routes } from '@angular/router';

export const SCHEDULES_ROUTES: Routes = [
  // Comment out missing component routes
  // {
  //   path: 'list',
  //   loadComponent: () => import('./schedule-list/schedule-list.component').then(m => m.ScheduleListComponent)
  // },
  // {
  //   path: 'new',
  //   loadComponent: () => import('./schedule-form/schedule-form.component').then(m => m.ScheduleFormComponent)
  // },
  // {
  //   path: 'edit/:id',
  //   loadComponent: () => import('./schedule-form/schedule-form.component').then(m => m.ScheduleFormComponent)
  // },
  // {
  //   path: ':id',
  //   loadComponent: () => import('./schedule-detail/schedule-detail.component').then(m => m.ScheduleDetailComponent)
  // },
]; 