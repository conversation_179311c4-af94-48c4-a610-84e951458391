{"version": 3, "file": "platform_navigation-B45Jeakb.mjs", "sources": ["../../../../../darwin_arm64-fastbuild-ST-2d99d9656325/bin/packages/common/src/navigation/platform_navigation.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  Injectable,\n  ɵNavigateEvent as NavigateEvent,\n  ɵNavigation as Navigation,\n  ɵNavigationCurrentEntryChangeEvent as NavigationCurrentEntryChangeEvent,\n  ɵNavigationHistoryEntry as NavigationHistoryEntry,\n  ɵNavigationNavigateOptions as NavigationNavigateOptions,\n  ɵNavigationOptions as NavigationOptions,\n  ɵNavigationReloadOptions as NavigationReloadOptions,\n  ɵNavigationResult as NavigationResult,\n  ɵNavigationTransition as NavigationTransition,\n  ɵNavigationUpdateCurrentEntryOptions as NavigationUpdateCurrentEntryOptions,\n} from '@angular/core';\n\n/**\n * This class wraps the platform Navigation API which allows server-specific and test\n * implementations.\n */\n@Injectable({providedIn: 'platform', useFactory: () => (window as any).navigation})\nexport abstract class PlatformNavigation implements Navigation {\n  abstract entries(): NavigationHistoryEntry[];\n  abstract currentEntry: NavigationHistoryEntry | null;\n  abstract updateCurrentEntry(options: NavigationUpdateCurrentEntryOptions): void;\n  abstract transition: NavigationTransition | null;\n  abstract canGoBack: boolean;\n  abstract canGoForward: boolean;\n  abstract navigate(url: string, options?: NavigationNavigateOptions | undefined): NavigationResult;\n  abstract reload(options?: NavigationReloadOptions | undefined): NavigationResult;\n  abstract traverseTo(key: string, options?: NavigationOptions | undefined): NavigationResult;\n  abstract back(options?: NavigationOptions | undefined): NavigationResult;\n  abstract forward(options?: NavigationOptions | undefined): NavigationResult;\n  abstract onnavigate: ((this: Navigation, ev: NavigateEvent) => any) | null;\n  abstract onnavigatesuccess: ((this: Navigation, ev: Event) => any) | null;\n  abstract onnavigateerror: ((this: Navigation, ev: ErrorEvent) => any) | null;\n  abstract oncurrententrychange:\n    | ((this: Navigation, ev: NavigationCurrentEntryChangeEvent) => any)\n    | null;\n  abstract addEventListener(type: unknown, listener: unknown, options?: unknown): void;\n  abstract removeEventListener(type: unknown, listener: unknown, options?: unknown): void;\n  abstract dispatchEvent(event: Event): boolean;\n}\n"], "names": [], "mappings": ";;;;;;;;;AAsBA;;;AAGG;MAEmB,kBAAkB,CAAA;kHAAlB,kBAAkB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA;sHAAlB,kBAAkB,EAAA,UAAA,EADf,UAAU,EAAc,UAAA,EAAA,MAAO,MAAc,CAAC,UAAU,EAAA,CAAA;;sGAC3D,kBAAkB,EAAA,UAAA,EAAA,CAAA;kBADvC,UAAU;AAAC,YAAA,IAAA,EAAA,CAAA,EAAC,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,MAAO,MAAc,CAAC,UAAU,EAAC;;;;;"}