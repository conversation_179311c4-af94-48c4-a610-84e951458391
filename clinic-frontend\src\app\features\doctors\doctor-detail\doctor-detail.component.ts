import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { DoctorService } from '../services/doctor.service';
import { Doctor } from '../models/doctor.model';

@Component({
  selector: 'app-doctor-detail',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './doctor-detail.component.html',
  styleUrls: ['./doctor-detail.component.scss']
})
export class DoctorDetailComponent implements OnInit {
  doctor: Doctor | null = null;
  loading = true;
  error: string | null = null;

  constructor(
    private doctorService: DoctorService,
    private route: ActivatedRoute,
    public router: Router
  ) {}

  ngOnInit(): void {
    const doctorId = this.route.snapshot.paramMap.get('id');
    if (doctorId) {
      this.loadDoctor(doctorId);
    } else {
      this.error = 'Doctor ID not provided';
      this.loading = false;
    }
  }

  private loadDoctor(id: string): void {
    this.doctorService.getDoctor(id).subscribe({
      next: (doctor) => {
        this.doctor = doctor;
        this.loading = false;
      },
      error: (error) => {
        console.error('Error loading doctor:', error);
        this.error = 'Failed to load doctor details';
        this.loading = false;
      }
    });
  }

  onDelete(): void {
    if (this.doctor && confirm('Are you sure you want to delete this doctor?')) {
      this.doctorService.deleteDoctor(this.doctor.id).subscribe({
        next: () => {
          this.router.navigate(['/doctors']);
        },
        error: (error) => {
          console.error('Error deleting doctor:', error);
          this.error = 'Failed to delete doctor';
        }
      });
    }
  }
} 