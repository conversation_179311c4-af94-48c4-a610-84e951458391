import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { PatientService } from '../services/patient.service';
import { Gender } from '../models/patient.model';

@Component({
  selector: 'app-patient-form',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule],
  templateUrl: './patient-form.component.html',
  styleUrls: ['./patient-form.component.scss']
})
export class PatientFormComponent implements OnInit {
  patientForm: FormGroup;
  isEditMode = false;
  patientId: string | null = null;
  loading = false;
  error: string | null = null;
  Gender = Gender; // Make Gender enum available in template

  constructor(
    private fb: FormBuilder,
    private patientService: PatientService,
    private route: ActivatedRoute,
    public router: Router // Make router public for template access
  ) {
    this.patientForm = this.fb.group({
      firstName: ['', Validators.required],
      lastName: ['', Validators.required],
      email: ['', [Validators.required, Validators.email]],
      phoneNumber: ['', Validators.required],
      dateOfBirth: ['', Validators.required],
      gender: [Gender.PreferNotToSay, Validators.required],
      address: ['', Validators.required],
      medicalHistory: [''],
      allergies: [[]]
    });
  }

  ngOnInit(): void {
    this.patientId = this.route.snapshot.paramMap.get('id');
    if (this.patientId) {
      this.isEditMode = true;
      this.loadPatient();
    }
  }

  private loadPatient(): void {
    if (!this.patientId) return;
    
    this.loading = true;
    this.error = null;
    
    this.patientService.getPatient(this.patientId).subscribe({
      next: (patient) => {
        this.patientForm.patchValue(patient);
        this.loading = false;
      },
      error: (error) => {
        this.error = 'Failed to load patient data';
        this.loading = false;
        console.error('Error loading patient:', error);
      }
    });
  }

  onSubmit(): void {
    if (this.patientForm.valid) {
      this.loading = true;
      this.error = null;
      const patientData = this.patientForm.value;
      
      const request$ = this.isEditMode && this.patientId
        ? this.patientService.updatePatient(this.patientId, patientData)
        : this.patientService.createPatient(patientData);

      request$.subscribe({
        next: () => {
          this.loading = false;
          this.router.navigate(['/patients']);
        },
        error: (error) => {
          this.error = 'Failed to save patient data';
          this.loading = false;
          console.error('Error saving patient:', error);
        }
      });
    }
  }
} 