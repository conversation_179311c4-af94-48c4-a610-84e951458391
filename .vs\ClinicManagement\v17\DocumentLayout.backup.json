{"Version": 1, "WorkspaceRootPath": "F:\\clinc\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{D0BB96D1-75EE-4922-B836-25F09B81CB55}|src\\ClinicManagement.WebAPI\\ClinicManagement.WebAPI.csproj|f:\\clinc\\src\\clinicmanagement.webapi\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{D0BB96D1-75EE-4922-B836-25F09B81CB55}|src\\ClinicManagement.WebAPI\\ClinicManagement.WebAPI.csproj|solutionrelative:src\\clinicmanagement.webapi\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{D0BB96D1-75EE-4922-B836-25F09B81CB55}|src\\ClinicManagement.WebAPI\\ClinicManagement.WebAPI.csproj|f:\\clinc\\src\\clinicmanagement.webapi\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{D0BB96D1-75EE-4922-B836-25F09B81CB55}|src\\ClinicManagement.WebAPI\\ClinicManagement.WebAPI.csproj|solutionrelative:src\\clinicmanagement.webapi\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{D0BB96D1-75EE-4922-B836-25F09B81CB55}|src\\ClinicManagement.WebAPI\\ClinicManagement.WebAPI.csproj|f:\\clinc\\src\\clinicmanagement.webapi\\controllers\\doctorscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{D0BB96D1-75EE-4922-B836-25F09B81CB55}|src\\ClinicManagement.WebAPI\\ClinicManagement.WebAPI.csproj|solutionrelative:src\\clinicmanagement.webapi\\controllers\\doctorscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{D0BB96D1-75EE-4922-B836-25F09B81CB55}|src\\ClinicManagement.WebAPI\\ClinicManagement.WebAPI.csproj|f:\\clinc\\src\\clinicmanagement.webapi\\controllers\\schedulescontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{D0BB96D1-75EE-4922-B836-25F09B81CB55}|src\\ClinicManagement.WebAPI\\ClinicManagement.WebAPI.csproj|solutionrelative:src\\clinicmanagement.webapi\\controllers\\schedulescontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{D0BB96D1-75EE-4922-B836-25F09B81CB55}|src\\ClinicManagement.WebAPI\\ClinicManagement.WebAPI.csproj|f:\\clinc\\src\\clinicmanagement.webapi\\controllers\\patientscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{D0BB96D1-75EE-4922-B836-25F09B81CB55}|src\\ClinicManagement.WebAPI\\ClinicManagement.WebAPI.csproj|solutionrelative:src\\clinicmanagement.webapi\\controllers\\patientscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{D0BB96D1-75EE-4922-B836-25F09B81CB55}|src\\ClinicManagement.WebAPI\\ClinicManagement.WebAPI.csproj|f:\\clinc\\src\\clinicmanagement.webapi\\controllers\\medicalrecordscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{D0BB96D1-75EE-4922-B836-25F09B81CB55}|src\\ClinicManagement.WebAPI\\ClinicManagement.WebAPI.csproj|solutionrelative:src\\clinicmanagement.webapi\\controllers\\medicalrecordscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{D0BB96D1-75EE-4922-B836-25F09B81CB55}|src\\ClinicManagement.WebAPI\\ClinicManagement.WebAPI.csproj|f:\\clinc\\src\\clinicmanagement.webapi\\controllers\\authcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{D0BB96D1-75EE-4922-B836-25F09B81CB55}|src\\ClinicManagement.WebAPI\\ClinicManagement.WebAPI.csproj|solutionrelative:src\\clinicmanagement.webapi\\controllers\\authcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 8, "Children": [{"$type": "Bookmark", "Name": "ST:128:0:{75188d03-9892-4ae2-abf1-207126247ce5}"}, {"$type": "Bookmark", "Name": "ST:0:0:{1c4feeaa-4718-4aa9-859d-94ce25d182ba}"}, {"$type": "Document", "DocumentIndex": 1, "Title": "Program.cs", "DocumentMoniker": "F:\\clinc\\src\\ClinicManagement.WebAPI\\Program.cs", "RelativeDocumentMoniker": "src\\ClinicManagement.WebAPI\\Program.cs", "ToolTip": "F:\\clinc\\src\\ClinicManagement.WebAPI\\Program.cs", "RelativeToolTip": "src\\ClinicManagement.WebAPI\\Program.cs", "ViewState": "AgIAAA8AAAAAAAAAAAApwCMAAAAhAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-31T16:37:45.756Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "SchedulesController.cs", "DocumentMoniker": "F:\\clinc\\src\\ClinicManagement.WebAPI\\Controllers\\SchedulesController.cs", "RelativeDocumentMoniker": "src\\ClinicManagement.WebAPI\\Controllers\\SchedulesController.cs", "ToolTip": "F:\\clinc\\src\\ClinicManagement.WebAPI\\Controllers\\SchedulesController.cs", "RelativeToolTip": "src\\ClinicManagement.WebAPI\\Controllers\\SchedulesController.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-31T16:33:02.804Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "PatientsController.cs", "DocumentMoniker": "F:\\clinc\\src\\ClinicManagement.WebAPI\\Controllers\\PatientsController.cs", "RelativeDocumentMoniker": "src\\ClinicManagement.WebAPI\\Controllers\\PatientsController.cs", "ToolTip": "F:\\clinc\\src\\ClinicManagement.WebAPI\\Controllers\\PatientsController.cs", "RelativeToolTip": "src\\ClinicManagement.WebAPI\\Controllers\\PatientsController.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-31T16:33:00.061Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "MedicalRecordsController.cs", "DocumentMoniker": "F:\\clinc\\src\\ClinicManagement.WebAPI\\Controllers\\MedicalRecordsController.cs", "RelativeDocumentMoniker": "src\\ClinicManagement.WebAPI\\Controllers\\MedicalRecordsController.cs", "ToolTip": "F:\\clinc\\src\\ClinicManagement.WebAPI\\Controllers\\MedicalRecordsController.cs", "RelativeToolTip": "src\\ClinicManagement.WebAPI\\Controllers\\MedicalRecordsController.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-31T16:32:54.866Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "DoctorsController.cs", "DocumentMoniker": "F:\\clinc\\src\\ClinicManagement.WebAPI\\Controllers\\DoctorsController.cs", "RelativeDocumentMoniker": "src\\ClinicManagement.WebAPI\\Controllers\\DoctorsController.cs", "ToolTip": "F:\\clinc\\src\\ClinicManagement.WebAPI\\Controllers\\DoctorsController.cs", "RelativeToolTip": "src\\ClinicManagement.WebAPI\\Controllers\\DoctorsController.cs", "ViewState": "AgIAAFwAAAAAAAAAAAAwwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-31T16:32:09.328Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "AuthController.cs", "DocumentMoniker": "F:\\clinc\\src\\ClinicManagement.WebAPI\\Controllers\\AuthController.cs", "RelativeDocumentMoniker": "src\\ClinicManagement.WebAPI\\Controllers\\AuthController.cs", "ToolTip": "F:\\clinc\\src\\ClinicManagement.WebAPI\\Controllers\\AuthController.cs", "RelativeToolTip": "src\\ClinicManagement.WebAPI\\Controllers\\AuthController.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA0AAAAwAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-31T16:31:39.815Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "appsettings.json", "DocumentMoniker": "F:\\clinc\\src\\ClinicManagement.WebAPI\\appsettings.json", "RelativeDocumentMoniker": "src\\ClinicManagement.WebAPI\\appsettings.json", "ToolTip": "F:\\clinc\\src\\ClinicManagement.WebAPI\\appsettings.json", "RelativeToolTip": "src\\ClinicManagement.WebAPI\\appsettings.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAwAAAAjAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-05-31T16:28:03.368Z", "EditorCaption": ""}]}]}]}