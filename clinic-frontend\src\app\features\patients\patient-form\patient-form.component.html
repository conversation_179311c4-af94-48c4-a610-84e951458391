<div class="container mx-auto p-4">
  <h1 class="text-2xl font-bold mb-6">{{ isEditMode ? 'Edit' : 'Create' }} Patient</h1>

  <div *ngIf="error" class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
    {{ error }}
  </div>

  <form [formGroup]="patientForm" (ngSubmit)="onSubmit()" class="space-y-4">
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
      <div>
        <label class="block text-sm font-medium text-gray-700">First Name</label>
        <input type="text" formControlName="firstName" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
        <div *ngIf="patientForm.get('firstName')?.invalid && patientForm.get('firstName')?.touched" class="text-red-500 text-sm mt-1">
          First name is required
        </div>
      </div>

      <div>
        <label class="block text-sm font-medium text-gray-700">Last Name</label>
        <input type="text" formControlName="lastName" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
        <div *ngIf="patientForm.get('lastName')?.invalid && patientForm.get('lastName')?.touched" class="text-red-500 text-sm mt-1">
          Last name is required
        </div>
      </div>

      <div>
        <label class="block text-sm font-medium text-gray-700">Email</label>
        <input type="email" formControlName="email" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
        <div *ngIf="patientForm.get('email')?.invalid && patientForm.get('email')?.touched" class="text-red-500 text-sm mt-1">
          Please enter a valid email address
        </div>
      </div>

      <div>
        <label class="block text-sm font-medium text-gray-700">Phone Number</label>
        <input type="tel" formControlName="phoneNumber" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
        <div *ngIf="patientForm.get('phoneNumber')?.invalid && patientForm.get('phoneNumber')?.touched" class="text-red-500 text-sm mt-1">
          Phone number is required
        </div>
      </div>

      <div>
        <label class="block text-sm font-medium text-gray-700">Date of Birth</label>
        <input type="date" formControlName="dateOfBirth" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
        <div *ngIf="patientForm.get('dateOfBirth')?.invalid && patientForm.get('dateOfBirth')?.touched" class="text-red-500 text-sm mt-1">
          Date of birth is required
        </div>
      </div>

      <div>
        <label class="block text-sm font-medium text-gray-700">Gender</label>
        <select formControlName="gender" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
          <option [ngValue]="Gender.Male">Male</option>
          <option [ngValue]="Gender.Female">Female</option>
          <option [ngValue]="Gender.Other">Other</option>
          <option [ngValue]="Gender.PreferNotToSay">Prefer not to say</option>
        </select>
      </div>

      <div class="md:col-span-2">
        <label class="block text-sm font-medium text-gray-700">Address</label>
        <textarea formControlName="address" rows="3" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"></textarea>
        <div *ngIf="patientForm.get('address')?.invalid && patientForm.get('address')?.touched" class="text-red-500 text-sm mt-1">
          Address is required
        </div>
      </div>

      <div class="md:col-span-2">
        <label class="block text-sm font-medium text-gray-700">Medical History</label>
        <textarea formControlName="medicalHistory" rows="4" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"></textarea>
      </div>

      <div class="md:col-span-2">
        <label class="block text-sm font-medium text-gray-700">Allergies</label>
        <textarea formControlName="allergies" rows="2" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" placeholder="Enter allergies separated by commas"></textarea>
      </div>
    </div>

    <div class="flex justify-end space-x-4 mt-6">
      <button type="button" (click)="router.navigate(['/patients'])" class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
        Cancel
      </button>
      <button type="submit" [disabled]="patientForm.invalid || loading" class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50">
        <span *ngIf="loading" class="inline-block animate-spin mr-2">⟳</span>
        {{ isEditMode ? 'Update' : 'Create' }} Patient
      </button>
    </div>
  </form>
</div> 