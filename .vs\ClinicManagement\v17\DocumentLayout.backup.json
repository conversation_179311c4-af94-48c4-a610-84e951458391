{"Version": 1, "WorkspaceRootPath": "F:\\clinc\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{D0BB96D1-75EE-4922-B836-25F09B81CB55}|src\\ClinicManagement.WebAPI\\ClinicManagement.WebAPI.csproj|f:\\clinc\\src\\clinicmanagement.webapi\\controllers\\patientscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{D0BB96D1-75EE-4922-B836-25F09B81CB55}|src\\ClinicManagement.WebAPI\\ClinicManagement.WebAPI.csproj|solutionrelative:src\\clinicmanagement.webapi\\controllers\\patientscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{D0BB96D1-75EE-4922-B836-25F09B81CB55}|src\\ClinicManagement.WebAPI\\ClinicManagement.WebAPI.csproj|f:\\clinc\\src\\clinicmanagement.webapi\\controllers\\frontendpatientscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{D0BB96D1-75EE-4922-B836-25F09B81CB55}|src\\ClinicManagement.WebAPI\\ClinicManagement.WebAPI.csproj|solutionrelative:src\\clinicmanagement.webapi\\controllers\\frontendpatientscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{D0BB96D1-75EE-4922-B836-25F09B81CB55}|src\\ClinicManagement.WebAPI\\ClinicManagement.WebAPI.csproj|f:\\clinc\\src\\clinicmanagement.webapi\\controllers\\appointmentscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{D0BB96D1-75EE-4922-B836-25F09B81CB55}|src\\ClinicManagement.WebAPI\\ClinicManagement.WebAPI.csproj|solutionrelative:src\\clinicmanagement.webapi\\controllers\\appointmentscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 2, "Children": [{"$type": "Bookmark", "Name": "ST:128:0:{75188d03-9892-4ae2-abf1-207126247ce5}"}, {"$type": "Bookmark", "Name": "ST:0:0:{1c4feeaa-4718-4aa9-859d-94ce25d182ba}"}, {"$type": "Document", "DocumentIndex": 0, "Title": "PatientsController.cs", "DocumentMoniker": "F:\\clinc\\src\\ClinicManagement.WebAPI\\Controllers\\PatientsController.cs", "RelativeDocumentMoniker": "src\\ClinicManagement.WebAPI\\Controllers\\PatientsController.cs", "ToolTip": "F:\\clinc\\src\\ClinicManagement.WebAPI\\Controllers\\PatientsController.cs", "RelativeToolTip": "src\\ClinicManagement.WebAPI\\Controllers\\PatientsController.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAvwAkAAAA+AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-02T19:20:59.796Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "FrontendPatientsController.cs", "DocumentMoniker": "F:\\clinc\\src\\ClinicManagement.WebAPI\\Controllers\\FrontendPatientsController.cs", "RelativeDocumentMoniker": "src\\ClinicManagement.WebAPI\\Controllers\\FrontendPatientsController.cs", "ToolTip": "F:\\clinc\\src\\ClinicManagement.WebAPI\\Controllers\\FrontendPatientsController.cs", "RelativeToolTip": "src\\ClinicManagement.WebAPI\\Controllers\\FrontendPatientsController.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAgAAAA+AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-02T19:20:57.802Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "AppointmentsController.cs", "DocumentMoniker": "F:\\clinc\\src\\ClinicManagement.WebAPI\\Controllers\\AppointmentsController.cs", "RelativeDocumentMoniker": "src\\ClinicManagement.WebAPI\\Controllers\\AppointmentsController.cs", "ToolTip": "F:\\clinc\\src\\ClinicManagement.WebAPI\\Controllers\\AppointmentsController.cs", "RelativeToolTip": "src\\ClinicManagement.WebAPI\\Controllers\\AppointmentsController.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAcAAABCAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-02T19:20:54.549Z", "EditorCaption": ""}]}]}]}