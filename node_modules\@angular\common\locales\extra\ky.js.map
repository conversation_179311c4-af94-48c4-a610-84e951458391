{"version": 3, "file": "ky.js", "sourceRoot": "", "sources": ["ky.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,eAAe,CAAC,CAAC,CAAC,SAAS,EAAC,IAAI,EAAC,SAAS,EAAC,UAAU,EAAC,MAAM,EAAC,KAAK,CAAC,EAAC,CAAC,YAAY,EAAC,SAAS,EAAC,aAAa,EAAC,cAAc,EAAC,SAAS,EAAC,YAAY,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,YAAY,EAAC,SAAS,EAAC,aAAa,EAAC,cAAc,EAAC,UAAU,EAAC,KAAK,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,OAAO,EAAC,OAAO,EAAC,CAAC,OAAO,EAAC,OAAO,CAAC,EAAC,CAAC,OAAO,EAAC,OAAO,CAAC,EAAC,CAAC,OAAO,EAAC,OAAO,CAAC,EAAC,CAAC,OAAO,EAAC,OAAO,CAAC,CAAC,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\n\nexport default [[[\"түн орт\",\"чт\",\"эртң мн\",\"түшт кйн\",\"кечк\",\"түн\"],[\"түн ортосу\",\"чак түш\",\"эртең менен\",\"түштөн кийин\",\"кечинде\",\"түн ичинде\"],u],[[\"түн ортосу\",\"чак түш\",\"эртең менен\",\"түштөн кийин\",\"кечкурун\",\"түн\"],u,u],[\"00:00\",\"12:00\",[\"06:00\",\"12:00\"],[\"12:00\",\"18:00\"],[\"18:00\",\"21:00\"],[\"21:00\",\"06:00\"]]];\n"]}