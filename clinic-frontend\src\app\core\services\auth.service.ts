import { Injectable, inject } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BehaviorSubject, Observable, tap, catchError, throwError } from 'rxjs';
import { environment } from '../../../environments/environment';
import { UserRole, LoginRequest, LoginResponse, RegisterRequest, RefreshTokenResponse, User } from '../models/auth.model';
import { StorageService } from './storage.service';

// Remove duplicate interfaces - they're now imported from auth.model.ts

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  private http = inject(HttpClient);
  private storage = inject(StorageService);
  private isAuthenticatedSubject = new BehaviorSubject<boolean>(false);
  private currentUserSubject = new BehaviorSubject<User | null>(null);
  
  isAuthenticated$ = this.isAuthenticatedSubject.asObservable();
  currentUser$ = this.currentUserSubject.asObservable();

  constructor() {
    this.checkAuthStatus();
  }

  private checkAuthStatus(): void {
    const token = this.getToken();
    this.isAuthenticatedSubject.next(!!token);
    if (token) {
      this.loadCurrentUser();
    }
  }

  private loadCurrentUser(): void {
    this.http.get<User>(`${environment.apiUrl}/auth/me`).pipe(
      catchError((error) => {
        console.error('Failed to load current user:', error);
        this.logout();
        return throwError(() => error);
      })
    ).subscribe({
      next: (user) => this.currentUserSubject.next(user),
      error: () => this.logout()
    });
  }

  login(request: LoginRequest): Observable<LoginResponse> {
    return this.http.post<LoginResponse>(`${environment.apiUrl}/auth/login`, request)
      .pipe(
        tap((response: LoginResponse) => {
          this.storage.setItem('auth_token', response.token);
          this.storage.setItem('token_expiration', response.expiration);
          this.isAuthenticatedSubject.next(true);
          this.currentUserSubject.next(response.user);
        }),
        catchError((error) => {
          console.error('Login failed:', error);
          return throwError(() => error);
        })
      );
  }

  register(request: RegisterRequest): Observable<any> {
    return this.http.post(`${environment.apiUrl}/auth/register`, request);
  }

  initializeAdmin(request: RegisterRequest): Observable<any> {
    return this.http.post(`${environment.apiUrl}/auth/initialize-admin`, request);
  }

  refreshToken(): Observable<RefreshTokenResponse> {
    return this.http.post<RefreshTokenResponse>(`${environment.apiUrl}/auth/refresh-token`, {})
      .pipe(
        tap((response: RefreshTokenResponse) => {
          this.storage.setItem('auth_token', response.token);
          this.storage.setItem('token_expiration', response.expiration);
          this.currentUserSubject.next(response.user);
        }),
        catchError((error) => {
          console.error('Token refresh failed:', error);
          this.logout();
          return throwError(() => error);
        })
      );
  }

  logout(): void {
    this.storage.removeItem('auth_token');
    this.storage.removeItem('token_expiration');
    this.isAuthenticatedSubject.next(false);
    this.currentUserSubject.next(null);
  }

  getToken(): string | null {
    return this.storage.getItem('auth_token');
  }

  isAuthenticated(): boolean {
    return !!this.getToken();
  }

  hasRole(roles: UserRole[]): boolean {
    const user = this.currentUserSubject.value;
    return user ? roles.includes(user.role) : false;
  }
} 