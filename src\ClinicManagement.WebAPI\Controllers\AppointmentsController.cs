using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using ClinicManagement.Application.Features.Appointments.Commands.CreateAppointment;
using ClinicManagement.Application.Features.Appointments.Commands.UpdateAppointment;
using ClinicManagement.Application.Features.Appointments.Commands.CancelAppointment;
using ClinicManagement.Application.Features.Appointments.Queries.GetAppointmentById;
using ClinicManagement.Application.Features.Appointments.Queries.GetAppointmentsByDateRange;
using ClinicManagement.Application.Features.Appointments.Common;

namespace ClinicManagement.WebAPI.Controllers
{
    [Authorize]
    public class AppointmentsController : ApiControllerBase
    {
        /// <summary>
        /// Gets an appointment by ID
        /// </summary>
        /// <param name="id">Appointment ID</param>
        /// <returns>Appointment details</returns>
        [HttpGet("{id}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<AppointmentDto>> GetById(int id)
        {
            var query = new GetAppointmentByIdQuery { Id = id };
            var result = await Mediator.Send(query);

            return HandleResult(result);
        }

        /// <summary>
        /// Gets appointments by date range
        /// </summary>
        /// <param name="startDate">Start date</param>
        /// <param name="endDate">End date</param>
        /// <param name="doctorId">Optional doctor ID</param>
        /// <param name="patientId">Optional patient ID</param>
        /// <returns>List of appointments</returns>
        [HttpGet("date-range")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> GetByDateRange(
            [FromQuery] DateTime startDate,
            [FromQuery] DateTime endDate,
            [FromQuery] int? doctorId = null,
            [FromQuery] int? patientId = null)
        {
            if (startDate > endDate)
            {
                return BadRequest(new { message = "Start date must be before end date" });
            }

            var query = new GetAppointmentsByDateRangeQuery
            {
                StartDate = startDate,
                EndDate = endDate,
                DoctorId = doctorId,
                PatientId = patientId
            };

            var result = await Mediator.Send(query);
            return Ok(result);
        }

        /// <summary>
        /// Creates a new appointment
        /// </summary>
        /// <param name="command">Appointment data</param>
        /// <returns>ID of the created appointment</returns>
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<int>> Create(CreateAppointmentCommand command)
        {
            var result = await Mediator.Send(command);

            return CreatedResult(result, nameof(GetById), new { id = result.Value });
        }

        /// <summary>
        /// Updates an existing appointment
        /// </summary>
        /// <param name="id">Appointment ID</param>
        /// <param name="command">Updated appointment data</param>
        /// <returns>Success status</returns>
        [HttpPut("{id}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<bool>> Update(int id, UpdateAppointmentCommand command)
        {
            if (id != command.Id)
            {
                return BadRequest("Appointment ID in URL does not match the ID in the request body");
            }

            var result = await Mediator.Send(command);
            return HandleResult(result);
        }

        /// <summary>
        /// Cancels an appointment
        /// </summary>
        /// <param name="id">Appointment ID</param>
        /// <param name="command">Cancellation details</param>
        /// <returns>Success status</returns>
        [HttpDelete("{id}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<bool>> Cancel(int id, CancelAppointmentCommand command)
        {
            command.Id = id; // Ensure the ID matches the URL parameter
            var result = await Mediator.Send(command);
            return HandleResult(result);
        }

        /// <summary>
        /// Cancels an appointment (alternative endpoint for simple cancellation)
        /// </summary>
        /// <param name="id">Appointment ID</param>
        /// <returns>Success status</returns>
        [HttpPost("{id}/cancel")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<bool>> CancelAppointment(int id)
        {
            var command = new CancelAppointmentCommand { Id = id };
            var result = await Mediator.Send(command);
            return HandleResult(result);
        }
    }
}