using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using ClinicManagement.Application.Common.Interfaces;
using ClinicManagement.Application.Common.Models;
using ClinicManagement.Domain.Repositories;
using ClinicManagement.Domain.Exceptions;

namespace ClinicManagement.Application.Features.Patients.Commands.DeletePatient
{
    public class DeletePatientCommandHandler : ICommandHandler<DeletePatientCommand, bool>
    {
        private readonly IUnitOfWork _unitOfWork;

        public DeletePatientCommandHandler(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        public async Task<Result<bool>> Handle(DeletePatientCommand request, CancellationToken cancellationToken)
        {
            try
            {
                // Get the patient
                var patient = await _unitOfWork.Patients.GetByIdAsync(request.Id, cancellationToken);
                if (patient == null)
                {
                    return Result<bool>.Failure($"Patient with ID {request.Id} not found.");
                }

                // Check if patient has any appointments
                var appointments = await _unitOfWork.Appointments.ListAsync(
                    new Domain.Specifications.AppointmentsByPatientSpecification(request.Id), 
                    cancellationToken);

                if (appointments.Any())
                {
                    return Result<bool>.Failure("Cannot delete patient with existing appointments. Please cancel or complete all appointments first.");
                }

                // Check if patient has any medical records
                var medicalRecords = await _unitOfWork.MedicalRecords.ListAsync(
                    new Domain.Specifications.MedicalRecordsByPatientSpecification(request.Id), 
                    cancellationToken);

                if (medicalRecords.Any())
                {
                    return Result<bool>.Failure("Cannot delete patient with existing medical records. Please archive the records first.");
                }

                // Delete the patient
                await _unitOfWork.Patients.DeleteAsync(patient, cancellationToken);
                await _unitOfWork.SaveChangesAndDispatchEventsAsync(cancellationToken);

                return Result<bool>.Success(true);
            }
            catch (DomainException ex)
            {
                return Result<bool>.Failure(ex.Message);
            }
            catch (Exception ex)
            {
                return Result<bool>.Failure($"An error occurred while deleting the patient: {ex.Message}");
            }
        }
    }
}
