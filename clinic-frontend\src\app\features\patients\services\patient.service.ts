import { Injectable, inject } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../../environments/environment';
import { Patient } from '../models/patient.model';
import { PaginationQuery, PaginatedResponse } from '../../../core/models/api-response.model';

@Injectable({
  providedIn: 'root'
})
export class PatientService {
  private http = inject(HttpClient);
  private apiUrl = `${environment.apiUrl}/patients`;

  // Basic CRUD operations
  getPatients(): Observable<Patient[]> {
    return this.http.get<Patient[]>(this.apiUrl);
  }

  getPatient(id: string): Observable<Patient> {
    return this.http.get<Patient>(`${this.apiUrl}/${id}`);
  }

  createPatient(patient: Omit<Patient, 'id' | 'createdAt' | 'updatedAt'>): Observable<Patient> {
    return this.http.post<Patient>(this.apiUrl, patient);
  }

  updatePatient(id: string, patient: Partial<Patient>): Observable<Patient> {
    return this.http.put<Patient>(`${this.apiUrl}/${id}`, patient);
  }

  deletePatient(id: string): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/${id}`);
  }

  // Pagination and search
  getPaginatedPatients(pageNumber: number = 1, pageSize: number = 10, searchTerm?: string): Observable<PaginatedResponse<Patient>> {
    const query: PaginationQuery = {
      pageNumber,
      pageSize,
      searchTerm
    };
    return this.http.get<PaginatedResponse<Patient>>(`${this.apiUrl}/paginated`, { params: query as any });
  }

  searchPatients(searchTerm: string, pageNumber: number = 1, pageSize: number = 10): Observable<PaginatedResponse<Patient>> {
    return this.getPaginatedPatients(pageNumber, pageSize, searchTerm);
  }

  // Additional operations
  getPatientDetails(id: number): Observable<Patient> {
    return this.http.get<Patient>(`${this.apiUrl}/${id}/details`);
  }

  getPatientMedicalHistory(patientId: number): Observable<any> {
    return this.http.get<any>(`${this.apiUrl}/${patientId}/medical-history`);
  }
}