import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';

@Component({
  selector: 'app-medical-record-detail',
  standalone: true,
  imports: [CommonModule, RouterModule],
  template: `
    <div class="container mx-auto p-4">
      <h1 class="text-2xl font-bold mb-4">Medical Record Details</h1>
      <div class="grid gap-4">
        <!-- Placeholder for medical record details -->
        <p>Medical record details will be displayed here</p>
      </div>
      <div class="mt-4">
        <a routerLink=".." class="bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600">
          Back to List
        </a>
      </div>
    </div>
  `,
  styles: []
})
export class MedicalRecordDetailComponent implements OnInit {
  constructor() {}

  ngOnInit(): void {}
} 