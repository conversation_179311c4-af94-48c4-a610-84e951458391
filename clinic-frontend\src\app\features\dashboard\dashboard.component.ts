import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { PatientService } from '../../core/services/patient.service';
import { Patient, PatientListResponse } from '../../core/models/patient.model';

@Component({
  selector: 'app-dashboard',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="container mx-auto px-4 py-6">
      <h1 class="text-3xl font-bold text-gray-900 mb-6">Dashboard</h1>
      
      <div *ngIf="loading" class="flex justify-center items-center h-64">
        <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>
      </div>
      
      <div *ngIf="!loading">
        <!-- Recent Patients -->
        <div class="bg-white rounded-lg shadow mb-8">
          <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-xl font-semibold text-gray-800">Recent Patients</h2>
          </div>
          <div class="p-6">
            <div *ngIf="recentPatients.length === 0" class="text-center py-6 text-gray-500">
              No patients found.
            </div>
            <div *ngIf="recentPatients.length > 0" class="overflow-x-auto">
              <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                  <tr>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Phone</th>
                  </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                  <tr *ngFor="let patient of recentPatients">
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm font-medium text-gray-900">{{ patient.firstName }} {{ patient.lastName }}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm text-gray-900">{{ patient.email }}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm text-gray-900">{{ patient.phone }}</div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    :host {
      display: block;
    }
  `]
})
export class DashboardComponent implements OnInit {
  recentPatients: Patient[] = [];
  loading = false;
  error = '';

  constructor(private patientService: PatientService) {}

  ngOnInit(): void {
    this.loadRecentPatients();
  }

  loadRecentPatients(): void {
    this.loading = true;
    this.error = '';
    this.patientService.getAll(1, 5).subscribe({
      next: (response: PatientListResponse) => {
        this.recentPatients = response.patients;
        this.loading = false;
      },
      error: (error: any) => {
        this.error = 'Failed to load recent patients. Please try again.';
        this.loading = false;
        console.error('Error loading recent patients:', error);
      }
    });
  }
} 