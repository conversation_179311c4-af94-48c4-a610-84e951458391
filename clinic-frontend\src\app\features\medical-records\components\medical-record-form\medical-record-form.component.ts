import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

@Component({
  selector: 'app-medical-record-form',
  standalone: true,
  imports: [CommonModule, RouterModule, FormsModule, ReactiveFormsModule],
  template: `
    <div class="container mx-auto p-4">
      <h1 class="text-2xl font-bold mb-4">Medical Record Form</h1>
      <form class="space-y-4">
        <!-- Placeholder for form fields -->
        <div class="grid gap-4">
          <p>Form fields will be added here</p>
        </div>
        <div class="flex gap-4">
          <button type="submit" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
            Save
          </button>
          <a routerLink=".." class="bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600">
            Cancel
          </a>
        </div>
      </form>
    </div>
  `,
  styles: []
})
export class MedicalRecordFormComponent implements OnInit {
  constructor() {}

  ngOnInit(): void {}
} 