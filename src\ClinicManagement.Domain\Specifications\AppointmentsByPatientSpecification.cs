using ClinicManagement.Domain.Entities;

namespace ClinicManagement.Domain.Specifications
{
    public class AppointmentsByPatientSpecification : BaseSpecification<Appointment>
    {
        public AppointmentsByPatientSpecification(int patientId)
            : base(a => a.PatientId == patientId)
        {
            AddInclude(a => a.Doctor);
            AddInclude(a => a.Patient);
            ApplyOrderBy(a => a.AppointmentDate);
        }
    }
}
