using System;
using ClinicManagement.Application.Common.Interfaces;
using ClinicManagement.Domain.Enums;

namespace ClinicManagement.Application.Features.Patients.Commands.UpdatePatient
{
    public class UpdatePatientCommand : ICommand<bool>
    {
        public int Id { get; set; }
        public required string FirstName { get; set; }
        public required string LastName { get; set; }
        public string? MiddleName { get; set; }
        public DateTime DateOfBirth { get; set; }
        public Gender Gender { get; set; }
        public required string PhoneNumber { get; set; }
        public required string Email { get; set; }
        public required string Street { get; set; }
        public required string City { get; set; }
        public required string State { get; set; }
        public required string PostalCode { get; set; }
        public string Country { get; set; } = "USA";
        public string MedicalHistory { get; set; } = string.Empty;
        public string? EmergencyContactName { get; set; }
        public string? EmergencyContactPhone { get; set; }
        public string? InsuranceProvider { get; set; }
        public string? InsurancePolicyNumber { get; set; }
        public string? Allergies { get; set; }
    }
}
