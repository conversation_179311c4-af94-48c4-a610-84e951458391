using ClinicManagement.Domain.Entities;

namespace ClinicManagement.Domain.Specifications
{
    public class MedicalRecordsByPatientSpecification : BaseSpecification<MedicalRecord>
    {
        public MedicalRecordsByPatientSpecification(int patientId)
            : base(mr => mr.PatientId == patientId)
        {
            AddInclude(mr => mr.Patient);
            AddInclude(mr => mr.Appointment);
            AddInclude("Appointment.Doctor");
            ApplyOrderByDescending(mr => mr.CreatedAt);
        }
    }
}
