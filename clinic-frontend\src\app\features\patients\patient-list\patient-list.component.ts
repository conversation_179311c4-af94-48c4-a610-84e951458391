import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { PatientService } from '../services/patient.service';
import { Patient } from '../models/patient.model';

@Component({
  selector: 'app-patient-list',
  standalone: true,
  imports: [CommonModule, RouterModule],
  template: `
    <div class="patients-container">
      <div class="search-container">
        <input
          type="text"
          class="search-input"
          placeholder="Search patients..."
          (input)="onSearch($event)"
        />
      </div>

      <div *ngIf="loading" class="flex justify-center items-center h-64">
        <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>
      </div>

      <div *ngIf="!loading">
        <table class="patients-table">
          <thead>
            <tr>
              <th>Name</th>
              <th>Email</th>
              <th>Phone</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let patient of patients">
              <td>{{ patient.firstName }} {{ patient.lastName }}</td>
              <td>{{ patient.email }}</td>
              <td>{{ patient.phoneNumber }}</td>
              <td>
                <a [routerLink]="['/patients', patient.id]" class="action-link">View</a>
                <a [routerLink]="['/patients', patient.id, 'edit']" class="action-link">Edit</a>
                <a (click)="deletePatient(patient.id)" class="action-link delete-link">Delete</a>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  `,
  styles: [`
    .patients-container {
      padding: 2rem;
    }

    .search-container {
      margin-bottom: 2rem;
    }

    .search-input {
      width: 100%;
      max-width: 400px;
      padding: 0.5rem;
      border: 1px solid #d1d5db;
      border-radius: 0.375rem;
    }

    .search-input:focus {
      outline: none;
      border-color: #3b82f6;
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }

    .patients-table {
      width: 100%;
      border-collapse: collapse;
    }

    .patients-table th {
      text-align: left;
      padding: 0.75rem;
      background-color: #f9fafb;
      border-bottom: 2px solid #e5e7eb;
    }

    .patients-table td {
      padding: 0.75rem;
      border-bottom: 1px solid #e5e7eb;
    }

    .patients-table tr:hover {
      background-color: #f9fafb;
    }

    .action-link {
      color: #3b82f6;
      text-decoration: none;
      margin-right: 1rem;
      cursor: pointer;
    }

    .action-link:hover {
      text-decoration: underline;
    }

    .delete-link {
      color: #dc2626;
    }
  `]
})
export class PatientListComponent implements OnInit {
  patients: Patient[] = [];
  loading = false;
  error = '';

  constructor(private patientService: PatientService) {}

  ngOnInit(): void {
    this.loadPatients();
  }

  loadPatients(): void {
    this.loading = true;
    this.error = '';
    this.patientService.getPatients().subscribe({
      next: (patients: Patient[]) => {
        this.patients = patients;
        this.loading = false;
      },
      error: (error: any) => {
        this.error = 'Failed to load patients. Please try again.';
        this.loading = false;
        console.error('Error loading patients:', error);
      }
    });
  }

  onSearch(event: Event): void {
    // Implement search functionality if needed
  }

  deletePatient(id: string): void {
    if (confirm('Are you sure you want to delete this patient?')) {
      this.patientService.deletePatient(id).subscribe({
        next: () => {
          this.loadPatients();
        },
        error: (error: any) => {
          this.error = 'Failed to delete patient. Please try again.';
          console.error('Error deleting patient:', error);
        }
      });
    }
  }
} 