using System;
using System.Threading;
using System.Threading.Tasks;
using ClinicManagement.Application.Common.Interfaces;
using ClinicManagement.Application.Common.Models;
using ClinicManagement.Domain.Repositories;
using ClinicManagement.Domain.Exceptions;

namespace ClinicManagement.Application.Features.Appointments.Commands.CancelAppointment
{
    public class CancelAppointmentCommandHandler : ICommandHandler<CancelAppointmentCommand, bool>
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ICurrentUserService _currentUserService;

        public CancelAppointmentCommandHandler(IUnitOfWork unitOfWork, ICurrentUserService currentUserService)
        {
            _unitOfWork = unitOfWork;
            _currentUserService = currentUserService;
        }

        public async Task<Result<bool>> Handle(CancelAppointmentCommand request, CancellationToken cancellationToken)
        {
            try
            {
                // Get the appointment
                var appointment = await _unitOfWork.Appointments.GetByIdAsync(request.Id, cancellationToken);
                if (appointment == null)
                {
                    return Result<bool>.Failure($"Appointment with ID {request.Id} not found.");
                }

                // Cancel the appointment
                appointment.Cancel(_currentUserService.UserName ?? "system", request.Reason);

                // Save changes
                await _unitOfWork.Appointments.UpdateAsync(appointment, cancellationToken);
                await _unitOfWork.SaveChangesAndDispatchEventsAsync(cancellationToken);

                return Result<bool>.Success(true);
            }
            catch (DomainException ex)
            {
                return Result<bool>.Failure(ex.Message);
            }
            catch (Exception ex)
            {
                return Result<bool>.Failure($"An error occurred while cancelling the appointment: {ex.Message}");
            }
        }
    }
}
