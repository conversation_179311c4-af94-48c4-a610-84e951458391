import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { DoctorService } from '../services/doctor.service';

@Component({
  selector: 'app-doctor-form',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule],
  templateUrl: './doctor-form.component.html',
  styleUrls: ['./doctor-form.component.scss']
})
export class DoctorFormComponent implements OnInit {
  doctorForm: FormGroup;
  isEditMode = false;
  doctorId: string | null = null;

  constructor(
    private fb: FormBuilder,
    private doctorService: DoctorService,
    private route: ActivatedRoute,
    public router: Router
  ) {
    this.doctorForm = this.fb.group({
      firstName: ['', Validators.required],
      lastName: ['', Validators.required],
      email: ['', [Validators.required, Validators.email]],
      phoneNumber: ['', Validators.required],
      specialization: ['', Validators.required],
      licenseNumber: ['', Validators.required]
    });
  }

  ngOnInit(): void {
    this.doctorId = this.route.snapshot.paramMap.get('id');
    if (this.doctorId) {
      this.isEditMode = true;
      this.loadDoctor();
    }
  }

  private loadDoctor(): void {
    if (this.doctorId) {
      this.doctorService.getDoctor(this.doctorId).subscribe({
        next: (doctor) => {
          this.doctorForm.patchValue(doctor);
        },
        error: (error) => {
          console.error('Error loading doctor:', error);
          this.router.navigate(['/doctors']);
        }
      });
    }
  }

  onSubmit(): void {
    if (this.doctorForm.valid) {
      const doctorData = this.doctorForm.value;
      
      if (this.isEditMode && this.doctorId) {
        this.doctorService.updateDoctor(this.doctorId, doctorData).subscribe({
          next: () => {
            this.router.navigate(['/doctors']);
          },
          error: (error) => {
            console.error('Error updating doctor:', error);
          }
        });
      } else {
        this.doctorService.createDoctor(doctorData).subscribe({
          next: () => {
            this.router.navigate(['/doctors']);
          },
          error: (error) => {
            console.error('Error creating doctor:', error);
          }
        });
      }
    }
  }
} 