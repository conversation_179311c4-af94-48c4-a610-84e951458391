using System;
using System.Threading;
using System.Threading.Tasks;
using ClinicManagement.Application.Common.Interfaces;
using ClinicManagement.Application.Common.Models;
using ClinicManagement.Domain.Repositories;
using ClinicManagement.Domain.ValueObjects;
using ClinicManagement.Domain.Exceptions;

namespace ClinicManagement.Application.Features.Patients.Commands.UpdatePatient
{
    public class UpdatePatientCommandHandler : ICommandHandler<UpdatePatientCommand, bool>
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ICurrentUserService _currentUserService;

        public UpdatePatientCommandHandler(IUnitOfWork unitOfWork, ICurrentUserService currentUserService)
        {
            _unitOfWork = unitOfWork;
            _currentUserService = currentUserService;
        }

        public async Task<Result<bool>> Handle(UpdatePatientCommand request, CancellationToken cancellationToken)
        {
            try
            {
                // Get the existing patient
                var patient = await _unitOfWork.Patients.GetByIdAsync(request.Id, cancellationToken);
                if (patient == null)
                {
                    return Result<bool>.Failure($"Patient with ID {request.Id} not found.");
                }

                // Check if another patient with the same email exists (excluding current patient)
                var existingPatientWithEmail = await _unitOfWork.Patients.GetByEmailAsync(request.Email, cancellationToken);
                if (existingPatientWithEmail != null && existingPatientWithEmail.Id != request.Id)
                {
                    return Result<bool>.Failure("A patient with this email already exists.");
                }

                // Create value objects
                var name = new PersonName(request.FirstName, request.LastName, request.MiddleName);
                var email = new Email(request.Email);
                var phoneNumber = new PhoneNumber(request.PhoneNumber);
                var address = new Address(request.Street, request.City, request.State, request.PostalCode, request.Country);

                // Update patient information using the correct method signature
                patient.UpdatePersonalInfo(name, request.Gender, phoneNumber, email, address, _currentUserService.UserName ?? "system");
                patient.UpdateMedicalHistory(request.MedicalHistory, _currentUserService.UserName ?? "system");

                // Update optional information
                if (!string.IsNullOrEmpty(request.EmergencyContactName) && !string.IsNullOrEmpty(request.EmergencyContactPhone))
                {
                    var emergencyPhone = new PhoneNumber(request.EmergencyContactPhone);
                    patient.SetEmergencyContact(request.EmergencyContactName, emergencyPhone, _currentUserService.UserName ?? "system");
                }

                if (!string.IsNullOrEmpty(request.InsuranceProvider))
                {
                    patient.SetInsuranceInfo(request.InsuranceProvider, request.InsurancePolicyNumber ?? "", _currentUserService.UserName ?? "system");
                }

                if (!string.IsNullOrEmpty(request.Allergies))
                {
                    patient.SetAllergies(request.Allergies, _currentUserService.UserName ?? "system");
                }

                // Save changes
                await _unitOfWork.Patients.UpdateAsync(patient, cancellationToken);
                await _unitOfWork.SaveChangesAndDispatchEventsAsync(cancellationToken);

                return Result<bool>.Success(true);
            }
            catch (DomainException ex)
            {
                return Result<bool>.Failure(ex.Message);
            }
            catch (Exception ex)
            {
                return Result<bool>.Failure($"An error occurred while updating the patient: {ex.Message}");
            }
        }
    }
}
