{"Version": 1, "WorkspaceRootPath": "F:\\clinc\\angular clinic\\clinic-frontend\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|F:\\clinc\\angular clinic\\clinic-frontend\\src\\app\\features\\doctors\\services\\doctor.service.ts||{0F2454B1-A556-402D-A7D0-1FDE7F99DEE0}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:src\\app\\features\\doctors\\services\\doctor.service.ts||{0F2454B1-A556-402D-A7D0-1FDE7F99DEE0}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 2, "Children": [{"$type": "Bookmark", "Name": "ST:128:0:{75188d03-9892-4ae2-abf1-207126247ce5}"}, {"$type": "Bookmark", "Name": "ST:0:0:{1c4feeaa-4718-4aa9-859d-94ce25d182ba}"}, {"$type": "Document", "DocumentIndex": 0, "Title": "doctor.service.ts", "DocumentMoniker": "F:\\clinc\\angular clinic\\clinic-frontend\\src\\app\\features\\doctors\\services\\doctor.service.ts", "RelativeDocumentMoniker": "src\\app\\features\\doctors\\services\\doctor.service.ts", "ToolTip": "F:\\clinc\\angular clinic\\clinic-frontend\\src\\app\\features\\doctors\\services\\doctor.service.ts", "RelativeToolTip": "src\\app\\features\\doctors\\services\\doctor.service.ts", "ViewState": "AgIAABkAAAAAAAAAAAAgwC4AAAAfAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003213|", "WhenOpened": "2025-05-31T12:01:28.761Z", "EditorCaption": ""}]}]}]}