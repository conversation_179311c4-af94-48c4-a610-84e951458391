{"version": 3, "file": "http.mjs", "sources": ["../../../../../darwin_arm64-fastbuild-ST-2d99d9656325/bin/packages/common/http/src/resource.ts", "../../../../../darwin_arm64-fastbuild-ST-2d99d9656325/bin/packages/common/http/src/transfer_cache.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  Injector,\n  Signal,\n  ɵResourceImpl as ResourceImpl,\n  inject,\n  linkedSignal,\n  assertInInjectionContext,\n  signal,\n  computed,\n  ResourceStreamItem,\n  type ValueEqualityFn,\n  ɵRuntimeError,\n  ɵRuntimeErrorCode,\n  ɵencapsulateResourceError as encapsulateResourceError,\n} from '@angular/core';\nimport type {Subscription} from 'rxjs';\n\nimport {HttpRequest} from './request';\nimport {HttpClient} from './client';\nimport {HttpErrorResponse, HttpEventType, HttpProgressEvent, HttpResponseBase} from './response';\nimport {HttpHeaders} from './headers';\nimport {HttpParams} from './params';\nimport {HttpResourceRef, HttpResourceOptions, HttpResourceRequest} from './resource_api';\n\n/**\n * Type for the `httpRequest` top-level function, which includes the call signatures for the JSON-\n * based `httpRequest` as well as sub-functions for `ArrayBuffer`, `Blob`, and `string` type\n * requests.\n *\n * @experimental 19.2\n */\nexport interface HttpResourceFn {\n  /**\n   * Create a `Resource` that fetches data with an HTTP GET request to the given URL.\n   *\n   * The resource will update when the URL changes via signals.\n   *\n   * Uses `HttpClient` to make requests and supports interceptors, testing, and the other features\n   * of the `HttpClient` API. Data is parsed as JSON by default - use a sub-function of\n   * `httpResource`, such as `httpResource.text()`, to parse the response differently.\n   *\n   * @experimental 19.2\n   */\n  <TResult = unknown>(\n    url: () => string | undefined,\n    options: HttpResourceOptions<TResult, unknown> & {defaultValue: NoInfer<TResult>},\n  ): HttpResourceRef<TResult>;\n\n  /**\n   * Create a `Resource` that fetches data with an HTTP GET request to the given URL.\n   *\n   * The resource will update when the URL changes via signals.\n   *\n   * Uses `HttpClient` to make requests and supports interceptors, testing, and the other features\n   * of the `HttpClient` API. Data is parsed as JSON by default - use a sub-function of\n   * `httpResource`, such as `httpResource.text()`, to parse the response differently.\n   *\n   * @experimental 19.2\n   */\n  <TResult = unknown>(\n    url: () => string | undefined,\n    options?: HttpResourceOptions<TResult, unknown>,\n  ): HttpResourceRef<TResult | undefined>;\n\n  /**\n   * Create a `Resource` that fetches data with the configured HTTP request.\n   *\n   * The resource will update when the request changes via signals.\n   *\n   * Uses `HttpClient` to make requests and supports interceptors, testing, and the other features\n   * of the `HttpClient` API. Data is parsed as JSON by default - use a sub-function of\n   * `httpResource`, such as `httpResource.text()`, to parse the response differently.\n   *\n   * @experimental 19.2\n   */\n  <TResult = unknown>(\n    request: () => HttpResourceRequest | undefined,\n    options: HttpResourceOptions<TResult, unknown> & {defaultValue: NoInfer<TResult>},\n  ): HttpResourceRef<TResult>;\n\n  /**\n   * Create a `Resource` that fetches data with the configured HTTP request.\n   *\n   * The resource will update when the request changes via signals.\n   *\n   * Uses `HttpClient` to make requests and supports interceptors, testing, and the other features\n   * of the `HttpClient` API. Data is parsed as JSON by default - use a sub-function of\n   * `httpResource`, such as `httpResource.text()`, to parse the response differently.\n   *\n   * @experimental 19.2\n   */\n  <TResult = unknown>(\n    request: () => HttpResourceRequest | undefined,\n    options?: HttpResourceOptions<TResult, unknown>,\n  ): HttpResourceRef<TResult | undefined>;\n\n  /**\n   * Create a `Resource` that fetches data with the configured HTTP request.\n   *\n   * The resource will update when the URL or request changes via signals.\n   *\n   * Uses `HttpClient` to make requests and supports interceptors, testing, and the other features\n   * of the `HttpClient` API. Data is parsed into an `ArrayBuffer`.\n   *\n   * @experimental 19.2\n   */\n  arrayBuffer: {\n    <TResult = ArrayBuffer>(\n      url: () => string | undefined,\n      options: HttpResourceOptions<TResult, ArrayBuffer> & {defaultValue: NoInfer<TResult>},\n    ): HttpResourceRef<TResult>;\n\n    <TResult = ArrayBuffer>(\n      url: () => string | undefined,\n      options?: HttpResourceOptions<TResult, ArrayBuffer>,\n    ): HttpResourceRef<TResult | undefined>;\n\n    <TResult = ArrayBuffer>(\n      request: () => HttpResourceRequest | undefined,\n      options: HttpResourceOptions<TResult, ArrayBuffer> & {defaultValue: NoInfer<TResult>},\n    ): HttpResourceRef<TResult>;\n\n    <TResult = ArrayBuffer>(\n      request: () => HttpResourceRequest | undefined,\n      options?: HttpResourceOptions<TResult, ArrayBuffer>,\n    ): HttpResourceRef<TResult | undefined>;\n  };\n\n  /**\n   * Create a `Resource` that fetches data with the configured HTTP request.\n   *\n   * The resource will update when the URL or request changes via signals.\n   *\n   * Uses `HttpClient` to make requests and supports interceptors, testing, and the other features\n   * of the `HttpClient` API. Data is parsed into a `Blob`.\n   *\n   * @experimental 19.2\n   */\n  blob: {\n    <TResult = Blob>(\n      url: () => string | undefined,\n      options: HttpResourceOptions<TResult, Blob> & {defaultValue: NoInfer<TResult>},\n    ): HttpResourceRef<TResult>;\n\n    <TResult = Blob>(\n      url: () => string | undefined,\n      options?: HttpResourceOptions<TResult, Blob>,\n    ): HttpResourceRef<TResult | undefined>;\n\n    <TResult = Blob>(\n      request: () => HttpResourceRequest | undefined,\n      options: HttpResourceOptions<TResult, Blob> & {defaultValue: NoInfer<TResult>},\n    ): HttpResourceRef<TResult>;\n\n    <TResult = Blob>(\n      request: () => HttpResourceRequest | undefined,\n      options?: HttpResourceOptions<TResult, Blob>,\n    ): HttpResourceRef<TResult | undefined>;\n  };\n\n  /**\n   * Create a `Resource` that fetches data with the configured HTTP request.\n   *\n   * The resource will update when the URL or request changes via signals.\n   *\n   * Uses `HttpClient` to make requests and supports interceptors, testing, and the other features\n   * of the `HttpClient` API. Data is parsed as a `string`.\n   *\n   * @experimental 19.2\n   */\n  text: {\n    <TResult = string>(\n      url: () => string | undefined,\n      options: HttpResourceOptions<TResult, string> & {defaultValue: NoInfer<TResult>},\n    ): HttpResourceRef<TResult>;\n\n    <TResult = string>(\n      url: () => string | undefined,\n      options?: HttpResourceOptions<TResult, string>,\n    ): HttpResourceRef<TResult | undefined>;\n\n    <TResult = string>(\n      request: () => HttpResourceRequest | undefined,\n      options: HttpResourceOptions<TResult, string> & {defaultValue: NoInfer<TResult>},\n    ): HttpResourceRef<TResult>;\n\n    <TResult = string>(\n      request: () => HttpResourceRequest | undefined,\n      options?: HttpResourceOptions<TResult, string>,\n    ): HttpResourceRef<TResult | undefined>;\n  };\n}\n\n/**\n * `httpResource` makes a reactive HTTP request and exposes the request status and response value as\n * a `WritableResource`. By default, it assumes that the backend will return JSON data. To make a\n * request that expects a different kind of data, you can use a sub-constructor of `httpResource`,\n * such as `httpResource.text`.\n *\n * @experimental 19.2\n * @initializerApiFunction\n */\nexport const httpResource: HttpResourceFn = (() => {\n  const jsonFn = makeHttpResourceFn<unknown>('json') as HttpResourceFn;\n  jsonFn.arrayBuffer = makeHttpResourceFn<ArrayBuffer>('arraybuffer');\n  jsonFn.blob = makeHttpResourceFn('blob');\n  jsonFn.text = makeHttpResourceFn('text');\n  return jsonFn;\n})();\n\n/**\n * The expected response type of the server.\n *\n * This is used to parse the response appropriately before returning it to\n * the requestee.\n */\ntype ResponseType = 'arraybuffer' | 'blob' | 'json' | 'text';\ntype RawRequestType = (() => string | undefined) | (() => HttpResourceRequest | undefined);\n\nfunction makeHttpResourceFn<TRaw>(responseType: ResponseType) {\n  return function httpResource<TResult = TRaw>(\n    request: RawRequestType,\n    options?: HttpResourceOptions<TResult, TRaw>,\n  ): HttpResourceRef<TResult> {\n    if (ngDevMode && !options?.injector) {\n      assertInInjectionContext(httpResource);\n    }\n    const injector = options?.injector ?? inject(Injector);\n    return new HttpResourceImpl(\n      injector,\n      () => normalizeRequest(request, responseType),\n      options?.defaultValue,\n      options?.parse as (value: unknown) => TResult,\n      options?.equal as ValueEqualityFn<unknown>,\n    ) as HttpResourceRef<TResult>;\n  };\n}\n\nfunction normalizeRequest(\n  request: RawRequestType,\n  responseType: ResponseType,\n): HttpRequest<unknown> | undefined {\n  let unwrappedRequest = typeof request === 'function' ? request() : request;\n  if (unwrappedRequest === undefined) {\n    return undefined;\n  } else if (typeof unwrappedRequest === 'string') {\n    unwrappedRequest = {url: unwrappedRequest};\n  }\n\n  const headers =\n    unwrappedRequest.headers instanceof HttpHeaders\n      ? unwrappedRequest.headers\n      : new HttpHeaders(\n          unwrappedRequest.headers as\n            | Record<string, string | number | Array<string | number>>\n            | undefined,\n        );\n\n  const params =\n    unwrappedRequest.params instanceof HttpParams\n      ? unwrappedRequest.params\n      : new HttpParams({fromObject: unwrappedRequest.params});\n\n  return new HttpRequest(\n    unwrappedRequest.method ?? 'GET',\n    unwrappedRequest.url,\n    unwrappedRequest.body ?? null,\n    {\n      headers,\n      params,\n      reportProgress: unwrappedRequest.reportProgress,\n      withCredentials: unwrappedRequest.withCredentials,\n      responseType,\n      context: unwrappedRequest.context,\n      transferCache: unwrappedRequest.transferCache,\n    },\n  );\n}\nclass HttpResourceImpl<T>\n  extends ResourceImpl<T, HttpRequest<unknown> | undefined>\n  implements HttpResourceRef<T>\n{\n  private client!: HttpClient;\n  private _headers = linkedSignal({\n    source: this.extRequest,\n    computation: () => undefined as HttpHeaders | undefined,\n  });\n  private _progress = linkedSignal({\n    source: this.extRequest,\n    computation: () => undefined as HttpProgressEvent | undefined,\n  });\n  private _statusCode = linkedSignal({\n    source: this.extRequest,\n    computation: () => undefined as number | undefined,\n  });\n\n  readonly headers = computed(() =>\n    this.status() === 'resolved' || this.status() === 'error' ? this._headers() : undefined,\n  );\n  readonly progress = this._progress.asReadonly();\n  readonly statusCode = this._statusCode.asReadonly();\n\n  constructor(\n    injector: Injector,\n    request: () => HttpRequest<T> | undefined,\n    defaultValue: T,\n    parse?: (value: unknown) => T,\n    equal?: ValueEqualityFn<unknown>,\n  ) {\n    super(\n      request,\n      ({params: request, abortSignal}) => {\n        let sub: Subscription;\n\n        // Track the abort listener so it can be removed if the Observable completes (as a memory\n        // optimization).\n        const onAbort = () => sub.unsubscribe();\n        abortSignal.addEventListener('abort', onAbort);\n\n        // Start off stream as undefined.\n        const stream = signal<ResourceStreamItem<T>>({value: undefined as T});\n        let resolve: ((value: Signal<ResourceStreamItem<T>>) => void) | undefined;\n        const promise = new Promise<Signal<ResourceStreamItem<T>>>((r) => (resolve = r));\n\n        const send = (value: ResourceStreamItem<T>): void => {\n          stream.set(value);\n          resolve?.(stream);\n          resolve = undefined;\n        };\n\n        sub = this.client.request(request!).subscribe({\n          next: (event) => {\n            switch (event.type) {\n              case HttpEventType.Response:\n                this._headers.set(event.headers);\n                this._statusCode.set(event.status);\n                try {\n                  send({value: parse ? parse(event.body) : (event.body as T)});\n                } catch (error) {\n                  send({error: encapsulateResourceError(error)});\n                }\n                break;\n              case HttpEventType.DownloadProgress:\n                this._progress.set(event);\n                break;\n            }\n          },\n          error: (error) => {\n            if (error instanceof HttpErrorResponse) {\n              this._headers.set(error.headers);\n              this._statusCode.set(error.status);\n            }\n\n            send({error});\n            abortSignal.removeEventListener('abort', onAbort);\n          },\n          complete: () => {\n            if (resolve) {\n              send({\n                error: new ɵRuntimeError(\n                  ɵRuntimeErrorCode.RESOURCE_COMPLETED_BEFORE_PRODUCING_VALUE,\n                  ngDevMode && 'Resource completed before producing a value',\n                ),\n              });\n            }\n            abortSignal.removeEventListener('abort', onAbort);\n          },\n        });\n\n        return promise;\n      },\n      defaultValue,\n      equal,\n      injector,\n    );\n    this.client = injector.get(HttpClient);\n  }\n\n  // This is a type only override of the method\n  declare hasValue: () => this is HttpResourceRef<Exclude<T, undefined>>;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  APP_BOOTSTRAP_LISTENER,\n  ApplicationRef,\n  inject,\n  InjectionToken,\n  makeState<PERSON><PERSON>,\n  Provider,\n  <PERSON><PERSON><PERSON>,\n  TransferState,\n  ɵformatRuntimeError as formatRuntimeError,\n  ɵperformanceMarkFeature as performanceMarkFeature,\n  ɵtruncateMiddle as truncateMiddle,\n  ɵRuntimeError as RuntimeError,\n} from '@angular/core';\nimport {Observable, of} from 'rxjs';\nimport {tap} from 'rxjs/operators';\n\nimport {RuntimeErrorCode} from './errors';\nimport {HttpHeaders} from './headers';\nimport {HTTP_ROOT_INTERCEPTOR_FNS, HttpHandlerFn} from './interceptor';\nimport {HttpRequest} from './request';\nimport {HttpEvent, HttpResponse} from './response';\nimport {HttpParams} from './params';\n\n/**\n * Options to configure how TransferCache should be used to cache requests made via HttpClient.\n *\n * @param includeHeaders Specifies which headers should be included into cached responses. No\n *     headers are included by default.\n * @param filter A function that receives a request as an argument and returns a boolean to indicate\n *     whether a request should be included into the cache.\n * @param includePostRequests Enables caching for POST requests. By default, only GET and HEAD\n *     requests are cached. This option can be enabled if POST requests are used to retrieve data\n *     (for example using GraphQL).\n * @param includeRequestsWithAuthHeaders Enables caching of requests containing either `Authorization`\n *     or `Proxy-Authorization` headers. By default, these requests are excluded from caching.\n *\n * @publicApi\n */\nexport type HttpTransferCacheOptions = {\n  includeHeaders?: string[];\n  filter?: (req: HttpRequest<unknown>) => boolean;\n  includePostRequests?: boolean;\n  includeRequestsWithAuthHeaders?: boolean;\n};\n\n/**\n * If your application uses different HTTP origins to make API calls (via `HttpClient`) on the server and\n * on the client, the `HTTP_TRANSFER_CACHE_ORIGIN_MAP` token allows you to establish a mapping\n * between those origins, so that `HttpTransferCache` feature can recognize those requests as the same\n * ones and reuse the data cached on the server during hydration on the client.\n *\n * **Important note**: the `HTTP_TRANSFER_CACHE_ORIGIN_MAP` token should *only* be provided in\n * the *server* code of your application (typically in the `app.server.config.ts` script). Angular throws an\n * error if it detects that the token is defined while running on the client.\n *\n * @usageNotes\n *\n * When the same API endpoint is accessed via `http://internal-domain.com:8080` on the server and\n * via `https://external-domain.com` on the client, you can use the following configuration:\n * ```ts\n * // in app.server.config.ts\n * {\n *     provide: HTTP_TRANSFER_CACHE_ORIGIN_MAP,\n *     useValue: {\n *         'http://internal-domain.com:8080': 'https://external-domain.com'\n *     }\n * }\n * ```\n *\n * @publicApi\n */\nexport const HTTP_TRANSFER_CACHE_ORIGIN_MAP = new InjectionToken<Record<string, string>>(\n  ngDevMode ? 'HTTP_TRANSFER_CACHE_ORIGIN_MAP' : '',\n);\n\n/**\n * Keys within cached response data structure.\n */\n\nexport const BODY = 'b';\nexport const HEADERS = 'h';\nexport const STATUS = 's';\nexport const STATUS_TEXT = 'st';\nexport const REQ_URL = 'u';\nexport const RESPONSE_TYPE = 'rt';\n\ninterface TransferHttpResponse {\n  /** body */\n  [BODY]: any;\n  /** headers */\n  [HEADERS]: Record<string, string[]>;\n  /** status */\n  [STATUS]?: number;\n  /** statusText */\n  [STATUS_TEXT]?: string;\n  /** url */\n  [REQ_URL]?: string;\n  /** responseType */\n  [RESPONSE_TYPE]?: HttpRequest<unknown>['responseType'];\n}\n\ninterface CacheOptions extends HttpTransferCacheOptions {\n  isCacheActive: boolean;\n}\n\nconst CACHE_OPTIONS = new InjectionToken<CacheOptions>(\n  ngDevMode ? 'HTTP_TRANSFER_STATE_CACHE_OPTIONS' : '',\n);\n\n/**\n * A list of allowed HTTP methods to cache.\n */\nconst ALLOWED_METHODS = ['GET', 'HEAD'];\n\nexport function transferCacheInterceptorFn(\n  req: HttpRequest<unknown>,\n  next: HttpHandlerFn,\n): Observable<HttpEvent<unknown>> {\n  const {isCacheActive, ...globalOptions} = inject(CACHE_OPTIONS);\n  const {transferCache: requestOptions, method: requestMethod} = req;\n\n  // In the following situations we do not want to cache the request\n  if (\n    !isCacheActive ||\n    requestOptions === false ||\n    // POST requests are allowed either globally or at request level\n    (requestMethod === 'POST' && !globalOptions.includePostRequests && !requestOptions) ||\n    (requestMethod !== 'POST' && !ALLOWED_METHODS.includes(requestMethod)) ||\n    // Do not cache request that require authorization when includeRequestsWithAuthHeaders is falsey\n    (!globalOptions.includeRequestsWithAuthHeaders && hasAuthHeaders(req)) ||\n    globalOptions.filter?.(req) === false\n  ) {\n    return next(req);\n  }\n\n  const transferState = inject(TransferState);\n\n  const originMap: Record<string, string> | null = inject(HTTP_TRANSFER_CACHE_ORIGIN_MAP, {\n    optional: true,\n  });\n\n  if (typeof ngServerMode !== 'undefined' && !ngServerMode && originMap) {\n    throw new RuntimeError(\n      RuntimeErrorCode.HTTP_ORIGIN_MAP_USED_IN_CLIENT,\n      ngDevMode &&\n        'Angular detected that the `HTTP_TRANSFER_CACHE_ORIGIN_MAP` token is configured and ' +\n          'present in the client side code. Please ensure that this token is only provided in the ' +\n          'server code of the application.',\n    );\n  }\n\n  const requestUrl =\n    typeof ngServerMode !== 'undefined' && ngServerMode && originMap\n      ? mapRequestOriginUrl(req.url, originMap)\n      : req.url;\n\n  const storeKey = makeCacheKey(req, requestUrl);\n  const response = transferState.get(storeKey, null);\n\n  let headersToInclude = globalOptions.includeHeaders;\n  if (typeof requestOptions === 'object' && requestOptions.includeHeaders) {\n    // Request-specific config takes precedence over the global config.\n    headersToInclude = requestOptions.includeHeaders;\n  }\n\n  if (response) {\n    const {\n      [BODY]: undecodedBody,\n      [RESPONSE_TYPE]: responseType,\n      [HEADERS]: httpHeaders,\n      [STATUS]: status,\n      [STATUS_TEXT]: statusText,\n      [REQ_URL]: url,\n    } = response;\n    // Request found in cache. Respond using it.\n    let body: ArrayBuffer | Blob | string | undefined = undecodedBody;\n\n    switch (responseType) {\n      case 'arraybuffer':\n        body = new TextEncoder().encode(undecodedBody).buffer;\n        break;\n      case 'blob':\n        body = new Blob([undecodedBody]);\n        break;\n    }\n\n    // We want to warn users accessing a header provided from the cache\n    // That HttpTransferCache alters the headers\n    // The warning will be logged a single time by HttpHeaders instance\n    let headers = new HttpHeaders(httpHeaders);\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      // Append extra logic in dev mode to produce a warning when a header\n      // that was not transferred to the client is accessed in the code via `get`\n      // and `has` calls.\n      headers = appendMissingHeadersDetection(req.url, headers, headersToInclude ?? []);\n    }\n\n    return of(\n      new HttpResponse({\n        body,\n        headers,\n        status,\n        statusText,\n        url,\n      }),\n    );\n  }\n\n  // Request not found in cache. Make the request and cache it if on the server.\n  return next(req).pipe(\n    tap((event: HttpEvent<unknown>) => {\n      if (event instanceof HttpResponse && typeof ngServerMode !== 'undefined' && ngServerMode) {\n        transferState.set<TransferHttpResponse>(storeKey, {\n          [BODY]: event.body,\n          [HEADERS]: getFilteredHeaders(event.headers, headersToInclude),\n          [STATUS]: event.status,\n          [STATUS_TEXT]: event.statusText,\n          [REQ_URL]: requestUrl,\n          [RESPONSE_TYPE]: req.responseType,\n        });\n      }\n    }),\n  );\n}\n\n/** @returns true when the requests contains autorization related headers. */\nfunction hasAuthHeaders(req: HttpRequest<unknown>): boolean {\n  return req.headers.has('authorization') || req.headers.has('proxy-authorization');\n}\n\nfunction getFilteredHeaders(\n  headers: HttpHeaders,\n  includeHeaders: string[] | undefined,\n): Record<string, string[]> {\n  if (!includeHeaders) {\n    return {};\n  }\n\n  const headersMap: Record<string, string[]> = {};\n  for (const key of includeHeaders) {\n    const values = headers.getAll(key);\n    if (values !== null) {\n      headersMap[key] = values;\n    }\n  }\n\n  return headersMap;\n}\n\nfunction sortAndConcatParams(params: HttpParams | URLSearchParams): string {\n  return [...params.keys()]\n    .sort()\n    .map((k) => `${k}=${params.getAll(k)}`)\n    .join('&');\n}\n\nfunction makeCacheKey(\n  request: HttpRequest<any>,\n  mappedRequestUrl: string,\n): StateKey<TransferHttpResponse> {\n  // make the params encoded same as a url so it's easy to identify\n  const {params, method, responseType} = request;\n  const encodedParams = sortAndConcatParams(params);\n\n  let serializedBody = request.serializeBody();\n  if (serializedBody instanceof URLSearchParams) {\n    serializedBody = sortAndConcatParams(serializedBody);\n  } else if (typeof serializedBody !== 'string') {\n    serializedBody = '';\n  }\n\n  const key = [method, responseType, mappedRequestUrl, serializedBody, encodedParams].join('|');\n  const hash = generateHash(key);\n\n  return makeStateKey(hash);\n}\n\n/**\n * A method that returns a hash representation of a string using a variant of DJB2 hash\n * algorithm.\n *\n * This is the same hashing logic that is used to generate component ids.\n */\nfunction generateHash(value: string): string {\n  let hash = 0;\n\n  for (const char of value) {\n    hash = (Math.imul(31, hash) + char.charCodeAt(0)) << 0;\n  }\n\n  // Force positive number hash.\n  // ********** = equivalent of Integer.MAX_VALUE.\n  hash += ********** + 1;\n\n  return hash.toString();\n}\n\n/**\n * Returns the DI providers needed to enable HTTP transfer cache.\n *\n * By default, when using server rendering, requests are performed twice: once on the server and\n * other one on the browser.\n *\n * When these providers are added, requests performed on the server are cached and reused during the\n * bootstrapping of the application in the browser thus avoiding duplicate requests and reducing\n * load time.\n *\n */\nexport function withHttpTransferCache(cacheOptions: HttpTransferCacheOptions): Provider[] {\n  return [\n    {\n      provide: CACHE_OPTIONS,\n      useFactory: (): CacheOptions => {\n        performanceMarkFeature('NgHttpTransferCache');\n        return {isCacheActive: true, ...cacheOptions};\n      },\n    },\n    {\n      provide: HTTP_ROOT_INTERCEPTOR_FNS,\n      useValue: transferCacheInterceptorFn,\n      multi: true,\n    },\n    {\n      provide: APP_BOOTSTRAP_LISTENER,\n      multi: true,\n      useFactory: () => {\n        const appRef = inject(ApplicationRef);\n        const cacheState = inject(CACHE_OPTIONS);\n\n        return () => {\n          appRef.whenStable().then(() => {\n            cacheState.isCacheActive = false;\n          });\n        };\n      },\n    },\n  ];\n}\n\n/**\n * This function will add a proxy to an HttpHeader to intercept calls to get/has\n * and log a warning if the header entry requested has been removed\n */\nfunction appendMissingHeadersDetection(\n  url: string,\n  headers: HttpHeaders,\n  headersToInclude: string[],\n): HttpHeaders {\n  const warningProduced = new Set();\n  return new Proxy<HttpHeaders>(headers, {\n    get(target: HttpHeaders, prop: keyof HttpHeaders): unknown {\n      const value = Reflect.get(target, prop);\n      const methods: Set<keyof HttpHeaders> = new Set(['get', 'has', 'getAll']);\n\n      if (typeof value !== 'function' || !methods.has(prop)) {\n        return value;\n      }\n\n      return (headerName: string) => {\n        // We log when the key has been removed and a warning hasn't been produced for the header\n        const key = (prop + ':' + headerName).toLowerCase(); // e.g. `get:cache-control`\n        if (!headersToInclude.includes(headerName) && !warningProduced.has(key)) {\n          warningProduced.add(key);\n          const truncatedUrl = truncateMiddle(url);\n\n          // TODO: create Error guide for this warning\n          console.warn(\n            formatRuntimeError(\n              RuntimeErrorCode.HEADERS_ALTERED_BY_TRANSFER_CACHE,\n              `Angular detected that the \\`${headerName}\\` header is accessed, but the value of the header ` +\n                `was not transferred from the server to the client by the HttpTransferCache. ` +\n                `To include the value of the \\`${headerName}\\` header for the \\`${truncatedUrl}\\` request, ` +\n                `use the \\`includeHeaders\\` list. The \\`includeHeaders\\` can be defined either ` +\n                `on a request level by adding the \\`transferCache\\` parameter, or on an application ` +\n                `level by adding the \\`httpCacheTransfer.includeHeaders\\` argument to the ` +\n                `\\`provideClientHydration()\\` call. `,\n            ),\n          );\n        }\n\n        // invoking the original method\n        return (value as Function).apply(target, [headerName]);\n      };\n    },\n  });\n}\n\nfunction mapRequestOriginUrl(url: string, originMap: Record<string, string>): string {\n  const origin = new URL(url, 'resolve://').origin;\n  const mappedOrigin = originMap[origin];\n  if (!mappedOrigin) {\n    return url;\n  }\n\n  if (typeof ngDevMode === 'undefined' || ngDevMode) {\n    verifyMappedOrigin(mappedOrigin);\n  }\n\n  return url.replace(origin, mappedOrigin);\n}\n\nfunction verifyMappedOrigin(url: string): void {\n  if (new URL(url, 'resolve://').pathname !== '/') {\n    throw new RuntimeError(\n      RuntimeErrorCode.HTTP_ORIGIN_MAP_CONTAINS_PATH,\n      'Angular detected a URL with a path segment in the value provided for the ' +\n        `\\`HTTP_TRANSFER_CACHE_ORIGIN_MAP\\` token: ${url}. The map should only contain origins ` +\n        'without any other segments.',\n    );\n  }\n}\n"], "names": ["ResourceImpl", "encapsulateResourceError", "ɵRuntimeError", "RuntimeError", "performanceMarkFeature", "truncateMiddle", "formatRuntimeError"], "mappings": ";;;;;;;;;;;;;AAyMA;;;;;;;;AAQG;AACU,MAAA,YAAY,GAAmB,CAAC,MAAK;AAChD,IAAA,MAAM,MAAM,GAAG,kBAAkB,CAAU,MAAM,CAAmB;AACpE,IAAA,MAAM,CAAC,WAAW,GAAG,kBAAkB,CAAc,aAAa,CAAC;AACnE,IAAA,MAAM,CAAC,IAAI,GAAG,kBAAkB,CAAC,MAAM,CAAC;AACxC,IAAA,MAAM,CAAC,IAAI,GAAG,kBAAkB,CAAC,MAAM,CAAC;AACxC,IAAA,OAAO,MAAM;AACf,CAAC;AAWD,SAAS,kBAAkB,CAAO,YAA0B,EAAA;AAC1D,IAAA,OAAO,SAAS,YAAY,CAC1B,OAAuB,EACvB,OAA4C,EAAA;AAE5C,QAAA,IAAI,SAAS,IAAI,CAAC,OAAO,EAAE,QAAQ,EAAE;YACnC,wBAAwB,CAAC,YAAY,CAAC;;QAExC,MAAM,QAAQ,GAAG,OAAO,EAAE,QAAQ,IAAI,MAAM,CAAC,QAAQ,CAAC;QACtD,OAAO,IAAI,gBAAgB,CACzB,QAAQ,EACR,MAAM,gBAAgB,CAAC,OAAO,EAAE,YAAY,CAAC,EAC7C,OAAO,EAAE,YAAY,EACrB,OAAO,EAAE,KAAoC,EAC7C,OAAO,EAAE,KAAiC,CACf;AAC/B,KAAC;AACH;AAEA,SAAS,gBAAgB,CACvB,OAAuB,EACvB,YAA0B,EAAA;AAE1B,IAAA,IAAI,gBAAgB,GAAG,OAAO,OAAO,KAAK,UAAU,GAAG,OAAO,EAAE,GAAG,OAAO;AAC1E,IAAA,IAAI,gBAAgB,KAAK,SAAS,EAAE;AAClC,QAAA,OAAO,SAAS;;AACX,SAAA,IAAI,OAAO,gBAAgB,KAAK,QAAQ,EAAE;AAC/C,QAAA,gBAAgB,GAAG,EAAC,GAAG,EAAE,gBAAgB,EAAC;;AAG5C,IAAA,MAAM,OAAO,GACX,gBAAgB,CAAC,OAAO,YAAY;UAChC,gBAAgB,CAAC;UACjB,IAAI,WAAW,CACb,gBAAgB,CAAC,OAEJ,CACd;AAEP,IAAA,MAAM,MAAM,GACV,gBAAgB,CAAC,MAAM,YAAY;UAC/B,gBAAgB,CAAC;AACnB,UAAE,IAAI,UAAU,CAAC,EAAC,UAAU,EAAE,gBAAgB,CAAC,MAAM,EAAC,CAAC;AAE3D,IAAA,OAAO,IAAI,WAAW,CACpB,gBAAgB,CAAC,MAAM,IAAI,KAAK,EAChC,gBAAgB,CAAC,GAAG,EACpB,gBAAgB,CAAC,IAAI,IAAI,IAAI,EAC7B;QACE,OAAO;QACP,MAAM;QACN,cAAc,EAAE,gBAAgB,CAAC,cAAc;QAC/C,eAAe,EAAE,gBAAgB,CAAC,eAAe;QACjD,YAAY;QACZ,OAAO,EAAE,gBAAgB,CAAC,OAAO;QACjC,aAAa,EAAE,gBAAgB,CAAC,aAAa;AAC9C,KAAA,CACF;AACH;AACA,MAAM,gBACJ,SAAQA,aAAiD,CAAA;AAGjD,IAAA,MAAM;IACN,QAAQ,GAAG,YAAY,CAAC;QAC9B,MAAM,EAAE,IAAI,CAAC,UAAU;AACvB,QAAA,WAAW,EAAE,MAAM,SAAoC;AACxD,KAAA,CAAC;IACM,SAAS,GAAG,YAAY,CAAC;QAC/B,MAAM,EAAE,IAAI,CAAC,UAAU;AACvB,QAAA,WAAW,EAAE,MAAM,SAA0C;AAC9D,KAAA,CAAC;IACM,WAAW,GAAG,YAAY,CAAC;QACjC,MAAM,EAAE,IAAI,CAAC,UAAU;AACvB,QAAA,WAAW,EAAE,MAAM,SAA+B;AACnD,KAAA,CAAC;AAEO,IAAA,OAAO,GAAG,QAAQ,CAAC,MAC1B,IAAI,CAAC,MAAM,EAAE,KAAK,UAAU,IAAI,IAAI,CAAC,MAAM,EAAE,KAAK,OAAO,GAAG,IAAI,CAAC,QAAQ,EAAE,GAAG,SAAS,CACxF;AACQ,IAAA,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE;AACtC,IAAA,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,UAAU,EAAE;IAEnD,WACE,CAAA,QAAkB,EAClB,OAAyC,EACzC,YAAe,EACf,KAA6B,EAC7B,KAAgC,EAAA;AAEhC,QAAA,KAAK,CACH,OAAO,EACP,CAAC,EAAC,MAAM,EAAE,OAAO,EAAE,WAAW,EAAC,KAAI;AACjC,YAAA,IAAI,GAAiB;;;YAIrB,MAAM,OAAO,GAAG,MAAM,GAAG,CAAC,WAAW,EAAE;AACvC,YAAA,WAAW,CAAC,gBAAgB,CAAC,OAAO,EAAE,OAAO,CAAC;;YAG9C,MAAM,MAAM,GAAG,MAAM,CAAwB,EAAC,KAAK,EAAE,SAAc,EAAC,CAAC;AACrE,YAAA,IAAI,OAAqE;AACzE,YAAA,MAAM,OAAO,GAAG,IAAI,OAAO,CAAgC,CAAC,CAAC,MAAM,OAAO,GAAG,CAAC,CAAC,CAAC;AAEhF,YAAA,MAAM,IAAI,GAAG,CAAC,KAA4B,KAAU;AAClD,gBAAA,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC;AACjB,gBAAA,OAAO,GAAG,MAAM,CAAC;gBACjB,OAAO,GAAG,SAAS;AACrB,aAAC;YAED,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,OAAQ,CAAC,CAAC,SAAS,CAAC;AAC5C,gBAAA,IAAI,EAAE,CAAC,KAAK,KAAI;AACd,oBAAA,QAAQ,KAAK,CAAC,IAAI;wBAChB,KAAK,aAAa,CAAC,QAAQ;4BACzB,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC;4BAChC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC;AAClC,4BAAA,IAAI;gCACF,IAAI,CAAC,EAAC,KAAK,EAAE,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,GAAI,KAAK,CAAC,IAAU,EAAC,CAAC;;4BAC5D,OAAO,KAAK,EAAE;gCACd,IAAI,CAAC,EAAC,KAAK,EAAEC,yBAAwB,CAAC,KAAK,CAAC,EAAC,CAAC;;4BAEhD;wBACF,KAAK,aAAa,CAAC,gBAAgB;AACjC,4BAAA,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,KAAK,CAAC;4BACzB;;iBAEL;AACD,gBAAA,KAAK,EAAE,CAAC,KAAK,KAAI;AACf,oBAAA,IAAI,KAAK,YAAY,iBAAiB,EAAE;wBACtC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC;wBAChC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC;;AAGpC,oBAAA,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC;AACb,oBAAA,WAAW,CAAC,mBAAmB,CAAC,OAAO,EAAE,OAAO,CAAC;iBAClD;gBACD,QAAQ,EAAE,MAAK;oBACb,IAAI,OAAO,EAAE;AACX,wBAAA,IAAI,CAAC;AACH,4BAAA,KAAK,EAAE,IAAIC,aAAa,wEAEtB,SAAS,IAAI,6CAA6C,CAC3D;AACF,yBAAA,CAAC;;AAEJ,oBAAA,WAAW,CAAC,mBAAmB,CAAC,OAAO,EAAE,OAAO,CAAC;iBAClD;AACF,aAAA,CAAC;AAEF,YAAA,OAAO,OAAO;AAChB,SAAC,EACD,YAAY,EACZ,KAAK,EACL,QAAQ,CACT;QACD,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAC,GAAG,CAAC,UAAU,CAAC;;AAKzC;;AC9UD;;;;;;;;;;;;;;;;;;;;;;;;;AAyBG;AACU,MAAA,8BAA8B,GAAG,IAAI,cAAc,CAC9D,SAAS,GAAG,gCAAgC,GAAG,EAAE;AAGnD;;AAEG;AAEI,MAAM,IAAI,GAAG,GAAG;AAChB,MAAM,OAAO,GAAG,GAAG;AACnB,MAAM,MAAM,GAAG,GAAG;AAClB,MAAM,WAAW,GAAG,IAAI;AACxB,MAAM,OAAO,GAAG,GAAG;AACnB,MAAM,aAAa,GAAG,IAAI;AAqBjC,MAAM,aAAa,GAAG,IAAI,cAAc,CACtC,SAAS,GAAG,mCAAmC,GAAG,EAAE,CACrD;AAED;;AAEG;AACH,MAAM,eAAe,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC;AAEvB,SAAA,0BAA0B,CACxC,GAAyB,EACzB,IAAmB,EAAA;IAEnB,MAAM,EAAC,aAAa,EAAE,GAAG,aAAa,EAAC,GAAG,MAAM,CAAC,aAAa,CAAC;IAC/D,MAAM,EAAC,aAAa,EAAE,cAAc,EAAE,MAAM,EAAE,aAAa,EAAC,GAAG,GAAG;;AAGlE,IAAA,IACE,CAAC,aAAa;AACd,QAAA,cAAc,KAAK,KAAK;;SAEvB,aAAa,KAAK,MAAM,IAAI,CAAC,aAAa,CAAC,mBAAmB,IAAI,CAAC,cAAc,CAAC;SAClF,aAAa,KAAK,MAAM,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC;;SAErE,CAAC,aAAa,CAAC,8BAA8B,IAAI,cAAc,CAAC,GAAG,CAAC,CAAC;QACtE,aAAa,CAAC,MAAM,GAAG,GAAG,CAAC,KAAK,KAAK,EACrC;AACA,QAAA,OAAO,IAAI,CAAC,GAAG,CAAC;;AAGlB,IAAA,MAAM,aAAa,GAAG,MAAM,CAAC,aAAa,CAAC;AAE3C,IAAA,MAAM,SAAS,GAAkC,MAAM,CAAC,8BAA8B,EAAE;AACtF,QAAA,QAAQ,EAAE,IAAI;AACf,KAAA,CAAC;IAEF,IAAI,OAAO,YAAY,KAAK,WAAW,IAAI,CAAC,YAAY,IAAI,SAAS,EAAE;QACrE,MAAM,IAAIC,aAAY,CAAA,IAAA,wDAEpB,SAAS;YACP,qFAAqF;gBACnF,yFAAyF;AACzF,gBAAA,iCAAiC,CACtC;;IAGH,MAAM,UAAU,GACd,OAAO,YAAY,KAAK,WAAW,IAAI,YAAY,IAAI;UACnD,mBAAmB,CAAC,GAAG,CAAC,GAAG,EAAE,SAAS;AACxC,UAAE,GAAG,CAAC,GAAG;IAEb,MAAM,QAAQ,GAAG,YAAY,CAAC,GAAG,EAAE,UAAU,CAAC;IAC9C,MAAM,QAAQ,GAAG,aAAa,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC;AAElD,IAAA,IAAI,gBAAgB,GAAG,aAAa,CAAC,cAAc;IACnD,IAAI,OAAO,cAAc,KAAK,QAAQ,IAAI,cAAc,CAAC,cAAc,EAAE;;AAEvE,QAAA,gBAAgB,GAAG,cAAc,CAAC,cAAc;;IAGlD,IAAI,QAAQ,EAAE;AACZ,QAAA,MAAM,EACJ,CAAC,IAAI,GAAG,aAAa,EACrB,CAAC,aAAa,GAAG,YAAY,EAC7B,CAAC,OAAO,GAAG,WAAW,EACtB,CAAC,MAAM,GAAG,MAAM,EAChB,CAAC,WAAW,GAAG,UAAU,EACzB,CAAC,OAAO,GAAG,GAAG,GACf,GAAG,QAAQ;;QAEZ,IAAI,IAAI,GAA4C,aAAa;QAEjE,QAAQ,YAAY;AAClB,YAAA,KAAK,aAAa;gBAChB,IAAI,GAAG,IAAI,WAAW,EAAE,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,MAAM;gBACrD;AACF,YAAA,KAAK,MAAM;gBACT,IAAI,GAAG,IAAI,IAAI,CAAC,CAAC,aAAa,CAAC,CAAC;gBAChC;;;;;AAMJ,QAAA,IAAI,OAAO,GAAG,IAAI,WAAW,CAAC,WAAW,CAAC;AAC1C,QAAA,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,EAAE;;;;AAIjD,YAAA,OAAO,GAAG,6BAA6B,CAAC,GAAG,CAAC,GAAG,EAAE,OAAO,EAAE,gBAAgB,IAAI,EAAE,CAAC;;AAGnF,QAAA,OAAO,EAAE,CACP,IAAI,YAAY,CAAC;YACf,IAAI;YACJ,OAAO;YACP,MAAM;YACN,UAAU;YACV,GAAG;AACJ,SAAA,CAAC,CACH;;;AAIH,IAAA,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CACnB,GAAG,CAAC,CAAC,KAAyB,KAAI;QAChC,IAAI,KAAK,YAAY,YAAY,IAAI,OAAO,YAAY,KAAK,WAAW,IAAI,YAAY,EAAE;AACxF,YAAA,aAAa,CAAC,GAAG,CAAuB,QAAQ,EAAE;AAChD,gBAAA,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI;gBAClB,CAAC,OAAO,GAAG,kBAAkB,CAAC,KAAK,CAAC,OAAO,EAAE,gBAAgB,CAAC;AAC9D,gBAAA,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM;AACtB,gBAAA,CAAC,WAAW,GAAG,KAAK,CAAC,UAAU;gBAC/B,CAAC,OAAO,GAAG,UAAU;AACrB,gBAAA,CAAC,aAAa,GAAG,GAAG,CAAC,YAAY;AAClC,aAAA,CAAC;;KAEL,CAAC,CACH;AACH;AAEA;AACA,SAAS,cAAc,CAAC,GAAyB,EAAA;AAC/C,IAAA,OAAO,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,IAAI,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,qBAAqB,CAAC;AACnF;AAEA,SAAS,kBAAkB,CACzB,OAAoB,EACpB,cAAoC,EAAA;IAEpC,IAAI,CAAC,cAAc,EAAE;AACnB,QAAA,OAAO,EAAE;;IAGX,MAAM,UAAU,GAA6B,EAAE;AAC/C,IAAA,KAAK,MAAM,GAAG,IAAI,cAAc,EAAE;QAChC,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC;AAClC,QAAA,IAAI,MAAM,KAAK,IAAI,EAAE;AACnB,YAAA,UAAU,CAAC,GAAG,CAAC,GAAG,MAAM;;;AAI5B,IAAA,OAAO,UAAU;AACnB;AAEA,SAAS,mBAAmB,CAAC,MAAoC,EAAA;AAC/D,IAAA,OAAO,CAAC,GAAG,MAAM,CAAC,IAAI,EAAE;AACrB,SAAA,IAAI;AACJ,SAAA,GAAG,CAAC,CAAC,CAAC,KAAK,CAAG,EAAA,CAAC,CAAI,CAAA,EAAA,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE;SACrC,IAAI,CAAC,GAAG,CAAC;AACd;AAEA,SAAS,YAAY,CACnB,OAAyB,EACzB,gBAAwB,EAAA;;IAGxB,MAAM,EAAC,MAAM,EAAE,MAAM,EAAE,YAAY,EAAC,GAAG,OAAO;AAC9C,IAAA,MAAM,aAAa,GAAG,mBAAmB,CAAC,MAAM,CAAC;AAEjD,IAAA,IAAI,cAAc,GAAG,OAAO,CAAC,aAAa,EAAE;AAC5C,IAAA,IAAI,cAAc,YAAY,eAAe,EAAE;AAC7C,QAAA,cAAc,GAAG,mBAAmB,CAAC,cAAc,CAAC;;AAC/C,SAAA,IAAI,OAAO,cAAc,KAAK,QAAQ,EAAE;QAC7C,cAAc,GAAG,EAAE;;AAGrB,IAAA,MAAM,GAAG,GAAG,CAAC,MAAM,EAAE,YAAY,EAAE,gBAAgB,EAAE,cAAc,EAAE,aAAa,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC;AAC7F,IAAA,MAAM,IAAI,GAAG,YAAY,CAAC,GAAG,CAAC;AAE9B,IAAA,OAAO,YAAY,CAAC,IAAI,CAAC;AAC3B;AAEA;;;;;AAKG;AACH,SAAS,YAAY,CAAC,KAAa,EAAA;IACjC,IAAI,IAAI,GAAG,CAAC;AAEZ,IAAA,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE;QACxB,IAAI,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,KAAK,CAAC;;;;AAKxD,IAAA,IAAI,IAAI,UAAU,GAAG,CAAC;AAEtB,IAAA,OAAO,IAAI,CAAC,QAAQ,EAAE;AACxB;AAEA;;;;;;;;;;AAUG;AACG,SAAU,qBAAqB,CAAC,YAAsC,EAAA;IAC1E,OAAO;AACL,QAAA;AACE,YAAA,OAAO,EAAE,aAAa;YACtB,UAAU,EAAE,MAAmB;gBAC7BC,uBAAsB,CAAC,qBAAqB,CAAC;gBAC7C,OAAO,EAAC,aAAa,EAAE,IAAI,EAAE,GAAG,YAAY,EAAC;aAC9C;AACF,SAAA;AACD,QAAA;AACE,YAAA,OAAO,EAAE,yBAAyB;AAClC,YAAA,QAAQ,EAAE,0BAA0B;AACpC,YAAA,KAAK,EAAE,IAAI;AACZ,SAAA;AACD,QAAA;AACE,YAAA,OAAO,EAAE,sBAAsB;AAC/B,YAAA,KAAK,EAAE,IAAI;YACX,UAAU,EAAE,MAAK;AACf,gBAAA,MAAM,MAAM,GAAG,MAAM,CAAC,cAAc,CAAC;AACrC,gBAAA,MAAM,UAAU,GAAG,MAAM,CAAC,aAAa,CAAC;AAExC,gBAAA,OAAO,MAAK;AACV,oBAAA,MAAM,CAAC,UAAU,EAAE,CAAC,IAAI,CAAC,MAAK;AAC5B,wBAAA,UAAU,CAAC,aAAa,GAAG,KAAK;AAClC,qBAAC,CAAC;AACJ,iBAAC;aACF;AACF,SAAA;KACF;AACH;AAEA;;;AAGG;AACH,SAAS,6BAA6B,CACpC,GAAW,EACX,OAAoB,EACpB,gBAA0B,EAAA;AAE1B,IAAA,MAAM,eAAe,GAAG,IAAI,GAAG,EAAE;AACjC,IAAA,OAAO,IAAI,KAAK,CAAc,OAAO,EAAE;QACrC,GAAG,CAAC,MAAmB,EAAE,IAAuB,EAAA;YAC9C,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC;AACvC,YAAA,MAAM,OAAO,GAA2B,IAAI,GAAG,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;AAEzE,YAAA,IAAI,OAAO,KAAK,KAAK,UAAU,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;AACrD,gBAAA,OAAO,KAAK;;YAGd,OAAO,CAAC,UAAkB,KAAI;;AAE5B,gBAAA,MAAM,GAAG,GAAG,CAAC,IAAI,GAAG,GAAG,GAAG,UAAU,EAAE,WAAW,EAAE,CAAC;AACpD,gBAAA,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;AACvE,oBAAA,eAAe,CAAC,GAAG,CAAC,GAAG,CAAC;AACxB,oBAAA,MAAM,YAAY,GAAGC,eAAc,CAAC,GAAG,CAAC;;AAGxC,oBAAA,OAAO,CAAC,IAAI,CACVC,mBAAkB,CAEhB,IAAA,2DAAA,CAAA,4BAAA,EAA+B,UAAU,CAAqD,mDAAA,CAAA;wBAC5F,CAA8E,4EAAA,CAAA;wBAC9E,CAAiC,8BAAA,EAAA,UAAU,CAAuB,oBAAA,EAAA,YAAY,CAAc,YAAA,CAAA;wBAC5F,CAAgF,8EAAA,CAAA;wBAChF,CAAqF,mFAAA,CAAA;wBACrF,CAA2E,yEAAA,CAAA;wBAC3E,CAAqC,mCAAA,CAAA,CACxC,CACF;;;gBAIH,OAAQ,KAAkB,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,UAAU,CAAC,CAAC;AACxD,aAAC;SACF;AACF,KAAA,CAAC;AACJ;AAEA,SAAS,mBAAmB,CAAC,GAAW,EAAE,SAAiC,EAAA;IACzE,MAAM,MAAM,GAAG,IAAI,GAAG,CAAC,GAAG,EAAE,YAAY,CAAC,CAAC,MAAM;AAChD,IAAA,MAAM,YAAY,GAAG,SAAS,CAAC,MAAM,CAAC;IACtC,IAAI,CAAC,YAAY,EAAE;AACjB,QAAA,OAAO,GAAG;;AAGZ,IAAA,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,EAAE;QACjD,kBAAkB,CAAC,YAAY,CAAC;;IAGlC,OAAO,GAAG,CAAC,OAAO,CAAC,MAAM,EAAE,YAAY,CAAC;AAC1C;AAEA,SAAS,kBAAkB,CAAC,GAAW,EAAA;AACrC,IAAA,IAAI,IAAI,GAAG,CAAC,GAAG,EAAE,YAAY,CAAC,CAAC,QAAQ,KAAK,GAAG,EAAE;QAC/C,MAAM,IAAIH,aAAY,CAAA,IAAA,uDAEpB,2EAA2E;AACzE,YAAA,CAAA,0CAAA,EAA6C,GAAG,CAAwC,sCAAA,CAAA;AACxF,YAAA,6BAA6B,CAChC;;AAEL;;;;"}