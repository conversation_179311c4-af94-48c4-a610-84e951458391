import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { Schedule, ScheduleListResponse, ScheduleRequest, DoctorSchedule } from '../../../core/models/schedule.model';
import { BaseHttpService } from '../../../core/services/base-http.service';

@Injectable({
  providedIn: 'root'
})
export class ScheduleService extends BaseHttpService<Schedule> {
  constructor(http: HttpClient) {
    super(http, 'schedules');
  }

  getPaginatedSchedules(pageNumber: number = 1, pageSize: number = 10): Observable<ScheduleListResponse> {
    let params: any = {
      pageNumber,
      pageSize
    };

    return this.http.get<ScheduleListResponse>(`${this.apiUrl}/paged`, { params });
  }

  getSchedulesByDoctor(doctorId: string): Observable<Schedule[]> {
    return this.http.get<Schedule[]>(`${this.apiUrl}/doctor/${doctorId}`);
  }

  getDoctorSchedule(doctorId: string): Observable<DoctorSchedule> {
    return this.http.get<DoctorSchedule>(`${this.apiUrl}/doctor-schedule/${doctorId}`);
  }

  getAvailableTimeSlots(doctorId: string, date: string): Observable<string[]> {
    return this.http.get<string[]>(`${this.apiUrl}/available-slots`, {
      params: {
        doctorId,
        date
      }
    });
  }

  createSchedule(schedule: ScheduleRequest): Observable<Schedule> {
    return this.create(schedule);
  }

  updateSchedule(id: string, schedule: Partial<ScheduleRequest>): Observable<Schedule> {
    return this.update(id, schedule);
  }

  toggleAvailability(id: string, isAvailable: boolean): Observable<Schedule> {
    return this.http.patch<Schedule>(`${this.apiUrl}/${id}/availability`, { isAvailable });
  }
} 