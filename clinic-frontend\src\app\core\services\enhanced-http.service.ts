import { Injectable } from '@angular/core';
import { HttpClient, HttpErrorResponse, HttpParams } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { environment } from '../../../environments/environment';
import { ApiResponse, PaginatedResponse, PaginationQuery, ApiError } from '../models/api-response.model';

@Injectable({
  providedIn: 'root'
})
export class EnhancedHttpService {
  protected readonly baseUrl = environment.apiUrl;

  constructor(protected http: HttpClient) {}

  get<T>(endpoint: string, params?: any): Observable<T> {
    const httpParams = this.buildHttpParams(params);
    return this.http.get<ApiResponse<T>>(`${this.baseUrl}${endpoint}`, { params: httpParams })
      .pipe(
        map(response => this.handleApiResponse(response)),
        catchError(this.handleError)
      );
  }

  post<T>(endpoint: string, data: any): Observable<T> {
    return this.http.post<ApiResponse<T>>(`${this.baseUrl}${endpoint}`, data)
      .pipe(
        map(response => this.handleApiResponse(response)),
        catchError(this.handleError)
      );
  }

  put<T>(endpoint: string, data: any): Observable<T> {
    return this.http.put<ApiResponse<T>>(`${this.baseUrl}${endpoint}`, data)
      .pipe(
        map(response => this.handleApiResponse(response)),
        catchError(this.handleError)
      );
  }

  delete<T>(endpoint: string): Observable<T> {
    return this.http.delete<ApiResponse<T>>(`${this.baseUrl}${endpoint}`)
      .pipe(
        map(response => this.handleApiResponse(response)),
        catchError(this.handleError)
      );
  }

  getPaginated<T>(endpoint: string, query: PaginationQuery): Observable<PaginatedResponse<T>> {
    const params = this.buildPaginationParams(query);
    return this.http.get<ApiResponse<PaginatedResponse<T>>>(`${this.baseUrl}${endpoint}`, { params })
      .pipe(
        map(response => this.handleApiResponse(response)),
        catchError(this.handleError)
      );
  }

  private handleApiResponse<T>(response: ApiResponse<T>): T {
    if (response.isSuccess && response.value !== undefined) {
      return response.value;
    }
    
    const errorMessage = response.error || 'An unknown error occurred';
    const errors = response.errors || [];
    throw new ApiError(errorMessage, 400, errors);
  }

  private handleError = (error: HttpErrorResponse): Observable<never> => {
    let apiError: ApiError;

    if (error.error instanceof ErrorEvent) {
      // Client-side error
      apiError = new ApiError(error.error.message, 0);
    } else {
      // Server-side error
      const errorMessage = error.error?.error || error.error?.message || error.statusText || 'Unknown error';
      const errors = error.error?.errors || [];
      apiError = new ApiError(errorMessage, error.status, errors);
    }

    console.error('HTTP Error:', apiError);
    return throwError(() => apiError);
  };

  private buildHttpParams(params?: any): HttpParams {
    let httpParams = new HttpParams();
    
    if (params) {
      Object.keys(params).forEach(key => {
        const value = params[key];
        if (value !== null && value !== undefined && value !== '') {
          if (Array.isArray(value)) {
            value.forEach(item => httpParams = httpParams.append(key, item.toString()));
          } else {
            httpParams = httpParams.set(key, value.toString());
          }
        }
      });
    }
    
    return httpParams;
  }

  private buildPaginationParams(query: PaginationQuery): HttpParams {
    let params = new HttpParams()
      .set('pageNumber', query.pageNumber.toString())
      .set('pageSize', query.pageSize.toString());

    if (query.searchTerm) {
      params = params.set('searchTerm', query.searchTerm);
    }

    if (query.sortBy) {
      params = params.set('sortBy', query.sortBy);
    }

    if (query.sortDescending !== undefined) {
      params = params.set('sortDescending', query.sortDescending.toString());
    }

    return params;
  }
}
