using System;
using System.Threading;
using System.Threading.Tasks;
using ClinicManagement.Application.Common.Interfaces;
using ClinicManagement.Application.Common.Models;
using ClinicManagement.Domain.Repositories;
using ClinicManagement.Domain.Exceptions;

namespace ClinicManagement.Application.Features.Appointments.Commands.UpdateAppointment
{
    public class UpdateAppointmentCommandHandler : ICommandHandler<UpdateAppointmentCommand, bool>
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ICurrentUserService _currentUserService;

        public UpdateAppointmentCommandHandler(IUnitOfWork unitOfWork, ICurrentUserService currentUserService)
        {
            _unitOfWork = unitOfWork;
            _currentUserService = currentUserService;
        }

        public async Task<Result<bool>> Handle(UpdateAppointmentCommand request, CancellationToken cancellationToken)
        {
            try
            {
                // Get the existing appointment
                var appointment = await _unitOfWork.Appointments.GetByIdAsync(request.Id, cancellationToken);
                if (appointment == null)
                {
                    return Result<bool>.Failure($"Appointment with ID {request.Id} not found.");
                }

                // Verify that the patient exists
                var patient = await _unitOfWork.Patients.GetByIdAsync(request.PatientId, cancellationToken);
                if (patient == null)
                {
                    return Result<bool>.Failure($"Patient with ID {request.PatientId} not found.");
                }

                // Verify that the doctor exists
                var doctor = await _unitOfWork.Doctors.GetByIdAsync(request.DoctorId, cancellationToken);
                if (doctor == null)
                {
                    return Result<bool>.Failure($"Doctor with ID {request.DoctorId} not found.");
                }

                // Update appointment details
                appointment.Reschedule(
                    request.AppointmentDate, 
                    request.StartTime, 
                    request.EndTime, 
                    _currentUserService.UserName ?? "system");

                // Update other properties if needed
                if (!string.IsNullOrEmpty(request.Notes))
                {
                    appointment.UpdateNotes(request.Notes, _currentUserService.UserName ?? "system");
                }

                // Save changes
                await _unitOfWork.Appointments.UpdateAsync(appointment, cancellationToken);
                await _unitOfWork.SaveChangesAndDispatchEventsAsync(cancellationToken);

                return Result<bool>.Success(true);
            }
            catch (DomainException ex)
            {
                return Result<bool>.Failure(ex.Message);
            }
            catch (Exception ex)
            {
                return Result<bool>.Failure($"An error occurred while updating the appointment: {ex.Message}");
            }
        }
    }
}
