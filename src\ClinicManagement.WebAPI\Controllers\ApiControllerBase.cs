using System.Linq;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.DependencyInjection;
using ClinicManagement.Application.Common.Models;

namespace ClinicManagement.WebAPI.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Produces("application/json")]
    public abstract class ApiControllerBase : ControllerBase
    {
        private ISender? _mediator;
        protected ISender Mediator => _mediator ??= HttpContext.RequestServices.GetRequiredService<ISender>();

        protected ActionResult<T> HandleResult<T>(Result<T> result)
        {
            if (result.IsSuccess)
            {
                return Ok(result);
            }

            if (result.Errors.Any())
            {
                return BadRequest(result);
            }

            return BadRequest(result);
        }

        protected ActionResult HandleResult(Result result)
        {
            if (result.IsSuccess)
            {
                return Ok(result);
            }

            if (result.Errors.Any())
            {
                return BadRequest(result);
            }

            return BadRequest(result);
        }

        protected ActionResult<T> CreatedResult<T>(Result<T> result, string actionName, object routeValues)
        {
            if (result.IsSuccess)
            {
                return CreatedAtAction(actionName, routeValues, result);
            }

            return HandleResult(result);
        }

        protected ActionResult<T> CreatedResult<T>(Result<T> result, string uri)
        {
            if (result.IsSuccess)
            {
                return Created(uri, result);
            }

            return HandleResult(result);
        }

        // Frontend-compatible methods that return unwrapped data
        protected ActionResult<T> HandleResultUnwrapped<T>(Result<T> result)
        {
            if (result.IsSuccess)
            {
                return Ok(result.Value);  // Return just the data, not the wrapper
            }

            if (result.Errors.Any())
            {
                return BadRequest(new { errors = result.Errors });
            }

            return BadRequest(new { error = result.Error });
        }

        protected ActionResult HandleResultUnwrapped(Result result)
        {
            if (result.IsSuccess)
            {
                return Ok();
            }

            if (result.Errors.Any())
            {
                return BadRequest(new { errors = result.Errors });
            }

            return BadRequest(new { error = result.Error });
        }

        protected ActionResult<T> CreatedResultUnwrapped<T>(Result<T> result, string actionName, object routeValues)
        {
            if (result.IsSuccess)
            {
                return CreatedAtAction(actionName, routeValues, result.Value);  // Return just the data
            }

            return HandleResultUnwrapped(result);
        }
    }
}