import { BaseModel } from './base.model';

export interface Schedule {
  id: string;
  doctorId: string;
  dayOfWeek: string;
  startTime: string;
  endTime: string;
  isAvailable: boolean;
  createdAt: string;
  updatedAt?: string;
}

export interface ScheduleListResponse {
  schedules: Schedule[];
  totalCount: number;
  pageNumber: number;
  pageSize: number;
}

export interface ScheduleRequest {
  doctorId: string;
  dayOfWeek: string;
  startTime: string;
  endTime: string;
  isAvailable: boolean;
}

export interface DoctorSchedule {
  doctorId: string;
  doctorName: string;
  schedules: Schedule[];
}

export enum DayOfWeek {
  Sunday = 0,
  Monday = 1,
  Tuesday = 2,
  Wednesday = 3,
  Thursday = 4,
  Friday = 5,
  Saturday = 6
} 