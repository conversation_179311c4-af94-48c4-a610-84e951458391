.patients-container {
  padding: 2rem;
}

.search-container {
  margin-bottom: 2rem;
}

.search-input {
  width: 100%;
  max-width: 400px;
  padding: 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
}

.search-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.patients-table {
  width: 100%;
  border-collapse: collapse;
}

.patients-table th {
  text-align: left;
  padding: 0.75rem;
  background-color: #f9fafb;
  border-bottom: 2px solid #e5e7eb;
}

.patients-table td {
  padding: 0.75rem;
  border-bottom: 1px solid #e5e7eb;
}

.patients-table tr:hover {
  background-color: #f9fafb;
}

.action-link {
  color: #3b82f6;
  text-decoration: none;
  margin-right: 1rem;
}

.action-link:hover {
  text-decoration: underline;
}

.delete-link {
  color: #dc2626;
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 2rem;
  gap: 0.5rem;
}

.pagination-button {
  padding: 0.5rem 1rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  background-color: white;
  cursor: pointer;
}

.pagination-button:hover {
  background-color: #f9fafb;
}

.pagination-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-info {
  color: #6b7280;
} 